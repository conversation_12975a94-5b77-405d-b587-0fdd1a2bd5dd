#!/usr/bin/env node

/**
 * 代码质量修复脚本
 * 自动修复一些常见的ESLint问题
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 获取项目根目录
const projectRoot = path.resolve(__dirname, '..');

// 需要处理的文件扩展名
const fileExtensions = ['.vue', '.ts', '.js'];

// 修复规则
const fixRules = [
  {
    name: '移除未使用的ElMessage组件注册',
    pattern: /^\s*ElMessage,?\s*$/gm,
    replacement: '',
    condition: content => !content.includes('ElMessage.') && !content.includes('ElMessage('),
  },
  {
    name: '移除未使用的ElMessageBox组件注册',
    pattern: /^\s*ElMessageBox,?\s*$/gm,
    replacement: '',
    condition: content => !content.includes('ElMessageBox.') && !content.includes('ElMessageBox('),
  },
  {
    name: '修复@ts-ignore为@ts-expect-error',
    pattern: /\/\/ @ts-ignore/g,
    replacement: '// @ts-expect-error',
  },
  {
    name: '修复Object.prototype方法调用',
    pattern: /(\w+)\.hasOwnProperty\(/g,
    replacement: 'Object.prototype.hasOwnProperty.call($1, ',
  },
];

/**
 * 递归获取所有需要处理的文件
 */
function getAllFiles(dir, fileList = []) {
  const files = fs.readdirSync(dir);

  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);

    if (stat.isDirectory()) {
      // 跳过node_modules和dist目录
      if (!['node_modules', 'dist', '.git'].includes(file)) {
        getAllFiles(filePath, fileList);
      }
    } else {
      // 检查文件扩展名
      const ext = path.extname(file);
      if (fileExtensions.includes(ext)) {
        fileList.push(filePath);
      }
    }
  });

  return fileList;
}

/**
 * 修复单个文件
 */
function fixFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;

    fixRules.forEach(rule => {
      if (rule.condition && !rule.condition(content)) {
        return;
      }

      const newContent = content.replace(rule.pattern, rule.replacement);
      if (newContent !== content) {
        content = newContent;
        modified = true;
        console.log(`✅ ${rule.name}: ${path.relative(projectRoot, filePath)}`);
      }
    });

    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      return true;
    }

    return false;
  } catch (error) {
    console.error(`❌ 处理文件失败: ${filePath}`, error.message);
    return false;
  }
}

/**
 * 主函数
 */
function main() {
  console.log('🚀 开始修复代码质量问题...\n');

  const srcDir = path.join(projectRoot, 'src');
  const files = getAllFiles(srcDir);

  console.log(`📁 找到 ${files.length} 个文件需要检查\n`);

  let fixedFiles = 0;
  const totalFixes = 0;

  files.forEach(file => {
    if (fixFile(file)) {
      fixedFiles++;
    }
  });

  console.log(`\n✨ 修复完成!`);
  console.log(`📊 统计信息:`);
  console.log(`   - 检查文件: ${files.length}`);
  console.log(`   - 修复文件: ${fixedFiles}`);

  if (fixedFiles > 0) {
    console.log('\n🔧 建议接下来运行:');
    console.log('   npm run lint:fix');
    console.log('   npm run format');
  }
}

// 运行脚本
main();
