# 线上多语言语料处理工具

这个工具用于从 Google Sheets 获取多语言翻译数据，并生成对应的语言文件。

## 前置条件

1. 安装依赖：

```bash
npm install googleapis
```

2. 配置 Google Sheets API：
   - 访问 [Google Cloud Console](https://console.cloud.google.com/)
   - 创建新项目或选择现有项目
   - 启用 Google Sheets API
   - 创建服务账号并下载凭证文件（JSON格式）
   - 将凭证文件重命名为 `credentials.json` 并放在 `language` 目录下
   - 将 Google Sheet 共享给服务账号邮箱（在凭证文件中可以找到）

## 配置说明

编辑 `config.json` 文件：

```json
{
  "googleSheetUrl": "你的Google Sheet URL",
  "range": {
    "startColumn": "A",     // 起始列
    "endColumn": "auto",    // 结束列（auto表示自动检测）
    "startRow": 1,          // 起始行
    "endRow": "auto"        // 结束行（auto表示自动检测）
  },
  "credentialsPath": "./credentials.json"  // 凭证文件路径
}
```

## 表格格式要求

Google Sheet 的格式应该如下：

- 第一行：语言代码（从第三列开始，如 zh、en 等）
- 第一列：翻译文本的key
- 第二列：描述（可选）
- 第三列及之后：各语言的翻译内容

## 使用方法

1. 基本使用：

```bash
node transLangFile.js
```

2. 指定游戏名称（生成到特定目录）：

```bash
node transLangFile.js --game=yourGameName
```

## 输出说明

- 生成的文件将保存在 `../src/assets/lang/` 目录下
- 如果指定了 game 参数，文件会保存在 `../src/assets/lang/[game]/` 目录下
- 每个语言会生成一个对应的 JSON 文件（如 zh.json、en.json）

## 错误处理

如果遇到错误，请检查：

1. credentials.json 文件是否存在且内容正确
2. Google Sheet 是否已共享给服务账号
3. config.json 中的 URL 是否正确
4. 表格格式是否符合要求

## 注意事项

1. 确保有足够的 Google Sheets API 配额
2. 建议定期备份生成的语言文件
3. 如果表格内容较多，首次运行可能需要较长时间

```
node transLangFile.js
```
