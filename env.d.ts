/// <reference types="vite/client" />

// 第三方库类型声明
declare module 'nprogress' {
  interface NProgress {
    start(): NProgress;
    done(): NProgress;
    set(n: number): NProgress;
    inc(n?: number): NProgress;
    configure(options: Partial<NProgressOptions>): NProgress;
  }

  interface NProgressOptions {
    minimum: number;
    template: string;
    easing: string;
    speed: number;
    trickle: boolean;
    trickleSpeed: number;
    showSpinner: boolean;
    barSelector: string;
    spinnerSelector: string;
    parent: string;
  }

  const nprogress: NProgress;
  export = nprogress;
}

declare module 'uuid' {
  export function v4(): string;
  export function v1(): string;
  export function v3(name: string, namespace: string): string;
  export function v5(name: string, namespace: string): string;
}

declare module 'quill-image-uploader' {
  const ImageUploader: any;
  export default ImageUploader;
}

declare module 'emoji-toolkit' {
  const emojiToolkit: any;
  export = emojiToolkit;
}

declare module 'favicon-badge' {
  const faviconBadge: any;
  export = faviconBadge;
}

// Vite环境变量类型
interface ImportMetaEnv {
  readonly VITE_APP_TITLE: string;
  readonly VITE_APP_API_BASE_URL: string;
  readonly VITE_APP_ENV: 'development' | 'production' | 'test';
}

interface ImportMeta {
  readonly env: ImportMetaEnv;
  globEager(pattern: string): Record<string, () => Promise<any>>;
  glob(pattern: string): Record<string, () => Promise<any>>;
}

// 全局类型声明
declare global {
  // API响应类型
  type ApiResponse<T = any> = {
    code: number;
    message: string;
    data: T;
    success: boolean;
  };

  // API函数类型
  type ApiT = (params?: Record<string, any>) => Promise<ApiResponse>;

  // 通用分页类型
  interface PaginationParams {
    page?: number;
    page_size?: number;
  }

  interface PaginationResponse<T = any> {
    list: T[];
    total: number;
    page: number;
    page_size: number;
  }
}
