server {
  listen 80;
  server_name opsticket-web-stage.funplus.com;
  error_log /dev/stdout warn;
  access_log /dev/stdout main;

  add_header Access-Control-Allow-Origin *;
  add_header Access-Control-Allow-Methods 'GET, POST, OPTIONS';
  add_header Access-Control-Allow-Headers 'DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization';

  if ($request_method = 'OPTIONS') {
    return 204;
  }

  location / {
    root  /data/nginx/dist;
    index  index.php index.html index.htm;
    try_files $uri $uri/ /index.html;
  }

  location ^~ /gateway/ {
    proxy_pass http://admin-gateway.ops-tools-stage.svc.cluster.local:8888/;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    client_max_body_size 8m;
    proxy_connect_timeout 300;
    proxy_send_timeout 300;
    proxy_read_timeout 300;
  }
  location = /50x.html {
      root   html;
  }
}