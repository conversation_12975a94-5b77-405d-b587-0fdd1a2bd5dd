{"name": "ops-ticket-web", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build-check": "run-p type-check \"build {@}\" --", "preview": "vite preview", "build": "vite build", "type-check": "vue-tsc --build --force", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write \"src/**/*.{js,ts,vue,json,css,scss,md}\"", "format:check": "prettier --check \"src/**/*.{js,ts,vue,json,css,scss,md}\"", "lint:fix": "npm run lint && npm run format"}, "dependencies": {"@better-scroll/core": "^2.5.1", "@better-scroll/mouse-wheel": "^2.5.1", "@better-scroll/pull-down": "^2.5.1", "@element-plus/icons-vue": "^2.3.1", "@vueup/vue-quill": "^1.2.0", "@vueuse/core": "^10.9.0", "axios": "^1.6.8", "dom-to-image": "^2.6.0", "echarts": "^5.6.0", "element-plus": "^2.6.3", "emoji-toolkit": "^8.0.0", "favicon-badge": "^2.0.0", "lottie-web": "^5.12.2", "markdown-it": "^14.1.0", "moment": "^2.30.1", "normalize.css": "^8.0.1", "nprogress": "^0.2.0", "pinia": "^2.1.7", "pinia-plugin-persistedstate": "^3.2.1", "quill-image-uploader": "^1.3.0", "rfdc": "^1.4.1", "screenfull": "^6.0.2", "sortablejs": "^1.15.6", "splitpanes": "^3.1.5", "uuid": "^9.0.1", "vue": "^3.4.21", "vue-echarts": "^7.0.3", "vue-i18n": "9", "vue-router": "^4.3.0", "vue3-emoji-picker": "^1.1.8", "xgplayer": "^3.0.17"}, "devDependencies": {"@rushstack/eslint-patch": "^1.3.3", "@tsconfig/node20": "^20.1.2", "@types/echarts": "^5.0.0", "@types/node": "^20.12.6", "@vitejs/plugin-legacy": "^5.3.2", "@vitejs/plugin-vue": "^5.0.4", "@vue/eslint-config-typescript": "^12.0.0", "@vue/tsconfig": "^0.5.1", "eslint": "^8.49.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.4.0", "eslint-plugin-vue": "^9.17.0", "npm-run-all2": "^6.1.2", "rollup-plugin-visualizer": "^5.12.0", "sass": "^1.74.1", "sass-loader": "^14.1.1", "terser": "^5.30.4", "typescript": "~5.4.0", "unplugin-auto-import": "^0.17.5", "unplugin-element-plus": "^0.8.0", "unplugin-vue-components": "^0.26.0", "vite": "^5.2.0", "vite-plugin-webpackchunkname": "^1.0.1", "vue-tsc": "^2.0.6"}}