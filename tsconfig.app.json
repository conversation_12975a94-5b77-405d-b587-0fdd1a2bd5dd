{
  "extends": "@vue/tsconfig/tsconfig.dom.json",
  "include": [
    "env.d.ts",
    "src/**/*",
    "*.ts",
    "src/**/*.ts",
    "src/**/**/*.ts",
    "src/**/**/**/*.ts",
    "src/**/*.vue",
    "src/**/**/*.vue",
    "src/**/**/**/*.vue",
    "src/**/**/**/*.json"
  ],
  "exclude": ["src/**/__tests__/*", "node_modules"],
  "compilerOptions": {
    "composite": true, // 启用复合项目
    "allowJs": true, // 允许js文件
    "tsBuildInfoFile": "./node_modules/.tmp/tsconfig.app.tsbuildinfo", // 构建信息文件
    "baseUrl": ".", // 基础路径
    "paths": {
      "@/*": ["./src/*"] // 路径别名
    },
    "strict": true, // 严格模式
    "noUnusedLocals": false, // 不检查未使用的局部变量
    "noUnusedParameters": false, // 不检查未使用的参数
    "noImplicitReturns": true, // 禁止隐式返回类型
    "noFallthroughCasesInSwitch": true, // 禁止switch语句中没有break的case
    "skipLibCheck": true, // 跳过库检查
    "resolveJsonModule": true, // 解析json模块
    "allowSyntheticDefaultImports": true, // 允许合成默认导入
    "esModuleInterop": true, // 允许es模块互操作
    "forceConsistentCasingInFileNames": true // 强制一致的文件名大小写
  }
}
