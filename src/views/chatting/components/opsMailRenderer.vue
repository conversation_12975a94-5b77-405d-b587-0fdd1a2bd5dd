<template>
  <div
    class="dc-chat-item-box"
    @mouseenter="toolsVisible = true"
    @mouseleave="toolsVisible = false"
  >
    <div class="dc-checkbox" v-if="props.dialogData.checked">
      <el-checkbox @change="handleGetMsgId(props.dialogData.message_id)" size="small"></el-checkbox>
    </div>
    <div class="dc-container">
      <div
        class="referenced-content"
        v-if="props.dialogData.referenced_msg_id"
        @click="referClickHandle"
      >
        <div class="referenced-line"></div>
        <template v-if="props.dialogData.referenced_msg">
          <div class="dc-chat-avatar">
            <el-avatar
              v-if="props.dialogData.referenced_msg.sender"
              src="https://kg-web-cdn.kingsgroupgames.com/prod/upload/tickets/d3fe4120930547f1b38482bb63b89b8a1719372248.png"
              class="ava"
            />
            <el-avatar
              v-else
              src="https://kg-web-cdn.kingsgroupgames.com/prod/upload/tickets/line.7854b16d86652ff547354f84b119d7a5.1735029202670221467.1263.png"
              class="ava avaPlayer"
            />
          </div>
          <div class="dc-referenced-cont">
            <div class="name">
              {{
                props.dialogData.referenced_msg.sender ||
                props.dialogData.referenced_msg.display_name
              }}
            </div>
            <div
              class="referenced-msg-content"
              v-html="
                joypixels.toImage(markdownRenderer(props.dialogData.referenced_msg.content))
                  ? joypixels.toImage(markdownRenderer(props.dialogData.referenced_msg.content))
                  : $t('text_click_show_tip')
              "
            ></div>
          </div>
        </template>
        <template v-else>
          <div>{{ $t('text_has_delete') }}</div>
        </template>
      </div>

      <div class="main-content">
        <div class="dc-chat-avatar">
          <el-avatar
            v-if="props.dialogData.sender"
            src="https://kg-web-cdn.kingsgroupgames.com/prod/upload/tickets/d3fe4120930547f1b38482bb63b89b8a1719372248.png"
            class="ava"
          />
          <el-avatar
            v-else
            src="https://kg-web-cdn.kingsgroupgames.com/prod/upload/tickets/line.7854b16d86652ff547354f84b119d7a5.1735029202670221467.1263.png"
            class="ava avaPlayer"
          />
        </div>
        <div class="dc-chat-cont">
          <div class="name-time">
            <span class="name">{{ props.dialogData.sender || props.dialogData.display_name }}</span>
            <span v-if="props.dialogData.is_edited" class="has-edit"
              >({{ $t('text_has_edit') }})</span
            >
            <span class="time">{{ props.dialogData.create_time }}</span>
          </div>
          <!-- 编辑文本内容 -->
          <div class="chat-container" v-if="props.dialogData.showEditContent && changeMsgId">
            <div class="chat-input-box">
              <div class="top-boxes" v-loading="contentLoading">
                <el-input
                  ref="textInput"
                  id="chat-input"
                  autosize
                  v-model="editContent"
                  type="textarea"
                  @input="updateCursorPosition"
                  @click="updateCursorPosition"
                  @keyup="updateCursorPosition"
                  :placeholder="$t('text_send_to')"
                />
                <div class="tool-box">
                  <!-- 表情功能 -->
                  <el-popover
                    :width="'auto'"
                    popper-style="box-shadow: rgb(14 18 22 / 35%) 0px 10px 38px -10px, rgb(14 18 22 / 20%) 0px 10px 20px -15px; padding: 0px; background: transparent;border-radius:8px;"
                  >
                    <template #reference>
                      <div class="emoji-btn tool-btn">
                        <svg
                          t="1720582777160"
                          class="icon"
                          viewBox="0 0 1024 1024"
                          version="1.1"
                          xmlns="http://www.w3.org/2000/svg"
                          p-id="4323"
                          width="17"
                          height="17"
                        >
                          <path
                            d="M512 0C229.2 0 0 229.2 0 512s229.2 512 512 512 512-229.2 512-512S794.8 0 512 0z m0 960C265 960 64 759 64 512S265 64 512 64s448 201 448 448-201 448-448 448z"
                            p-id="4324"
                            fill="currentColor"
                          ></path>
                          <path
                            d="M320 405.3m-64 0a64 64 0 1 0 128 0 64 64 0 1 0-128 0Z"
                            p-id="4325"
                            fill="currentColor"
                          ></path>
                          <path
                            d="M704 405.3m-64 0a64 64 0 1 0 128 0 64 64 0 1 0-128 0Z"
                            p-id="4326"
                            fill="currentColor"
                          ></path>
                          <path
                            d="M512 810.7c117.8 0 213.3-95.5 213.3-213.3H298.7c0 117.8 95.5 213.3 213.3 213.3z"
                            p-id="4327"
                            fill="currentColor"
                          ></path>
                        </svg>
                      </div>
                    </template>
                    <template #default>
                      <EmojiPicker
                        @select="addEmoji"
                        :disable-sticky-group-names="true"
                        :static-texts="{
                          placeholder: $t('text_search_emoji'),
                          skinTone: $t('text_skin_tone'),
                        }"
                        :group-names="{
                          recent: $t('text_recent'),
                          smileys_people: $t('text_peple'),
                          animals_nature: $t('text_nature'),
                          food_drink: $t('text_food'),
                          activities: $t('text_activities'),
                          travel_places: $t('text_travel'),
                          objects: $t('text_objects'),
                          symbols: $t('text_symbols'),
                          flags: $t('text_flags'),
                        }"
                        :display-recent="true"
                      />
                    </template>
                  </el-popover>
                </div>
              </div>
            </div>
            <div class="input-tips">
              <el-button type="danger" size="small" @click="handleCancel">{{
                $t('btn_cancel')
              }}</el-button>
              <!-- <el-button type="primary" size="small" :loading="contentLoading" @click="handleSave">{{ $t('text_save') }}</el-button> -->
            </div>
          </div>
          <div
            v-else
            :class="[
              'dc-chat-content',
              props.dialogData.sender === 'system' ? 'dc-chat-bot-content' : '',
            ]"
          >
            <!-- 工具栏渲染 -->
            <el-popover
              placement="right"
              :visible="toolsVisible && props.tooltip"
              :offset="1"
              :show-arrow="false"
              popper-class="custom-popper"
              :teleported="false"
            >
              <div v-if="!props.dialogData.checked" class="more">
                <el-button-group>
                  <el-tooltip
                    v-for="item in menuItems"
                    :key="item.id"
                    effect="dark"
                    :content="item.title"
                    placement="top"
                  >
                    <el-button
                      :icon="item.icon"
                      size="small"
                      :disabled="
                        item.id === '2' &&
                        (userInfo.username !== props.dialogData.sender ||
                          !Boolean(props.dialogData.content))
                      "
                      @click="menuClickHandle(item.id)"
                    />
                  </el-tooltip>
                </el-button-group>
              </div>
              <template #reference>
                <div class="dc-tool"></div>
              </template>
            </el-popover>
            <!-- 回复文字/markdown/emoji渲染 -->
            <div
              v-if="
                props.dialogData.content &&
                !(
                  props.dialogData.embed &&
                  props.dialogData.embed.length > 0 &&
                  props.dialogData.embed[0].type !== 'link' &&
                  props.dialogData.embed[0].type !== 'article'
                )
              "
              class="content-text"
              v-html="content ? renderDialogue(content) : ''"
            ></div>
            <!-- 贴图渲染 -->
            <lineSticker
              v-if="props.dialogData.sticker_info"
              :sticker-data="JSON.parse(props.dialogData.sticker_info)"
            />
            <!-- 回复附件渲染 -->
            <attachReader
              v-if="props.dialogData.original_content_url"
              :attach-data="{
                url: props.dialogData.original_content_url,
                content_type: props.dialogData.message_type,
                id: props.dialogData.message_id,
              }"
            />
            <!-- 回复嵌入渲染 -->
            <!-- <embedReader v-if="props.dialogData.embed && props.dialogData.embed.length > 0"
              v-for="item in props.dialogData.embed" :embed-data="item" /> -->
            <!-- 回复反应渲染 -->
            <!-- <div v-if="props.dialogData.reactions && props.dialogData.reactions.length > 0">
              <el-tag v-for="item in props.dialogData.reactions" class="dc-reaction" type="info"
                :style="{ 'background': props.dialogData.sender === 'system' ? 'rgb(187 208 245)' : 'rgb(209 212 215)'}"><span
                  v-html="joypixels.toImage(item.name)"></span> | <span style="color: #23262a;">{{ item.user_name
                  }}</span></el-tag>
            </div> -->
            <!-- 回复投票渲染 -->
            <!-- <pollReader v-if="props.dialogData.poll && props.dialogData.poll.answers"
              :poll-data="props.dialogData.poll" /> -->
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { useUserInfoStore } from '@/stores';
import { useI18n } from 'vue-i18n';
import EmojiPicker from 'vue3-emoji-picker';
import 'vue3-emoji-picker/css';
import joypixels from 'emoji-toolkit';
import markdownRenderer from '@/utils/markdownRenderer';
import lineSticker from './lineSticker.vue';
import attachReader from '@/packages/discordRenderer/components/attachReader.vue';
// import embedReader from '../../../packages/discordRenderer/components/embedReader.vue'
// import pollReader from '../../../packages/discordRenderer/components/pollReader.vue'
// import { discordMessageEdit } from '@/api/chatting'
interface DcDialogItem {
  sender: string; // 客服发送者
  display_name: string; // 玩家发送者
  original_content_url: string; // 原始内容url
  message_type: string; // 消息类型
  message_id: string; // 消息id
  create_time: string; // 创建时间

  attach: any[]; // 附件
  author: {
    avatar: string; // 头像
    bot: boolean; // 是否机器人
    global_name: string; // 用户名(有可能没有)
    id: string; // 用户id
    username: string; // 用户名
  };
  channel_id: string; // 会话id
  content: string; // 文字内容
  embed: any[]; // 嵌入
  from_user_id: string; // 发送者id
  is_edited: boolean; // 是否编辑过
  checked: boolean; // 是否展示复选框
  isEditing: boolean; // 是否正在编辑当前文本
  // msg_id: string // 消息id
  project: string; // 游戏
  reactions: any[]; // 表情
  referenced_msg?: DcDialogItem; // 引用消息
  referenced_msg_id: string; // 引用消息id
  sticker_info: ''; // 贴纸
  poll: any; // 投票
  showEditContent: boolean;
}
interface DcDialogItemProps {
  dialogData: DcDialogItem;
  tooltip?: boolean;
}
export default defineComponent({
  name: 'DiscordRenderer',
  components: {
    lineSticker,
    attachReader,
    // embedReader,
    // pollReader
  },
});
</script>
<script setup lang="ts">
const { t: $t } = useI18n();
const props = withDefaults(defineProps<DcDialogItemProps>(), {
  tooltip: true,
});
const emit = defineEmits<{
  (event: 'referenceClick', value: string): void;
  (event: 'checkboxChange', value: string): void;
  (event: 'menuClick', value: string, item: object): void;
}>();
const userInfo = computed(() => useUserInfoStore().userInfo as Record<string, unknown>);

const menuItems = [
  {
    id: '1',
    icon: 'Finished',
    title: $t('text_multiple_choice'),
  },
  // {
  //   id: '2',
  //   icon: 'Edit',
  //   title: $t('btn_edit')
  // },
  // {
  //   id: '3',
  //   icon: 'ChatDotSquare',
  //   title: $t('btn_reply')
  // }
];
const contentLoading = ref(false);
const content = ref<string>('');
watch(
  () => props.dialogData,
  val => {
    val.content = val.content.replace(/\n/g, '<br>'); // 增加换行
    if (val.embed && val.embed.length > 0 && val.embed[0].type === 'link') {
      content.value = joypixels.toImage(
        markdownRenderer(val.content.replace(val.embed[0].url, ''))
      );
    } else {
      content.value = joypixels.toImage(markdownRenderer(val.content));
    }
  },
  { immediate: true, deep: true }
);

const referClickHandle = () => {
  if (!props.dialogData.referenced_msg) {
    return;
  }
  emit('referenceClick', props.dialogData.referenced_msg!.message_id);
};

const handleGetMsgId = (val: string) => {
  emit('checkboxChange', val);
};

// 处理菜单项点击事件
const editContent = ref('');
const changeMsgId = ref('');
const currentEditing = ref(false); //用来标记表单是否正在提交
const menuClickHandle = (val: string) => {
  if (val === '1') {
    props.dialogData.checked = true;
  } else if (val === '2') {
    currentEditing.value = false;
    editContent.value = props.dialogData.content.replace(/<br>/g, '\n');
    changeMsgId.value = props.dialogData.message_id;
  }
  emit('menuClick', val, props.dialogData);
};

const cursorPosition = ref(null);
const textInput = ref(null) as any;
const updateCursorPosition = () => {
  const inputElement = textInput.value?.$refs.textarea;
  if (inputElement) {
    cursorPosition.value = inputElement.selectionStart;
  }
};

const addEmoji = (emoji: any) => {
  const inputElement = textInput.value?.$refs.textarea;
  if (cursorPosition.value !== null) {
    const beforeText = editContent.value.slice(0, cursorPosition.value);
    const afterText = editContent.value.slice(cursorPosition.value);
    editContent.value = beforeText + emoji.i + afterText;

    const newCursorPosition = cursorPosition.value + emoji.i.length;
    nextTick(() => {
      inputElement.focus();
      inputElement.setSelectionRange(newCursorPosition, newCursorPosition);
      updateCursorPosition();
    });
  } else {
    // 如果没有获取到光标位置，则在末尾添加 emoji
    editContent.value += emoji.i;
  }
};
// tool工具展示
const toolsVisible = ref<boolean>(false);
const handleCancel = () => {
  if (changeMsgId.value) {
    props.dialogData.showEditContent = false;
    currentEditing.value = true;
  }
};
// const handleSave = () => {
//   if (currentEditing.value) return
//   const { channel_id, author, showEditContent } = props.dialogData
//   if (changeMsgId.value && showEditContent) {
//     currentEditing.value = true
//     contentLoading.value = true
//     discordMessageEdit({
//       content: editContent.value,
//       channel_id,
//       bot_id: author.id,
//       msg_id: changeMsgId.value
//     }).then(() => {
//       ElMessage.success($t('text_edit_success'))
//       props.dialogData.showEditContent = false
//       currentEditing.value = true
//       contentLoading.value = false
//     }).catch((error: any) => {
//       console.error('Failed to edit message:', error)
//       currentEditing.value = false
//       contentLoading.value = false
//     })
//   }
// }

// 使用正则表达式遍历匹配的product_id和emoji_id
const regex = /product_id:([^,]+),emoji_id:([^,]+)/g;
const renderDialogue = (content: string) => {
  let newSentence: string = content;
  let matches: RegExpExecArray | null;
  while ((matches = regex.exec(content)) !== null) {
    const product_id = matches[1];
    const emoji_id = matches[2];
    const newUrl = `https://stickershop.line-scdn.net/sticonshop/v1/sticon/${product_id}/android/${emoji_id}.png`;
    newSentence = newSentence.replace(
      `product_id:${product_id},emoji_id:${emoji_id},`,
      `<img src="${newUrl}" alt="表情" width="24" height="24" />`
    );
  }
  return newSentence;
};
</script>

<style lang="scss" scoped>
.dc-chat-item-box {
  display: flex;
  align-items: center;
  width: 100%;
  height: auto;
  overflow: hidden;
  margin: 6px auto 10px;
  padding: 5px;
  .referenced-content {
    justify-content: flex-start;
    align-items: center;
    display: flex;
    font-size: 12px !important;
    height: 22px;
    line-height: 22px;
    overflow: hidden;
    opacity: 0.5;
    cursor: pointer;
    &:hover {
      opacity: 1;
    }
    .referenced-line {
      width: 26px;
      flex-shrink: 0;
      height: 12px;
      margin: 8px 3px 0px 13px;
      border: 2px solid #4aa181;
      border-right: 0px;
      border-bottom: 0px;
      border-top-left-radius: 6px;
    }
    .dc-chat-avatar {
      flex-shrink: 0;
      width: 15px;
      height: 15px;
      margin-right: 5px;
      float: none;
      .ava {
        width: 15px;
        height: 15px;
      }
    }
    .dc-referenced-cont {
      flex: 0 1 auto;
      /* 或者使用 min-width: 0 */
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      height: 100%;
      * {
        display: inline !important;
      }
      .name {
        margin-right: 5px;
      }
      .referenced-msg-content {
        &:deep(*) {
          display: inline !important;
        }
        &:deep(.joypixels) {
          height: 1rem;
          width: 1rem;
          vertical-align: middle;
        }
      }
    }
  }
  &:hover {
    .time {
      visibility: visible !important;
    }
  }
  .dc-checkbox {
    flex: 0 0 20px;
  }
  .dc-container {
    flex: 1;
    min-width: 0;
  }
  .dc-chat-avatar {
    float: left;
    width: 30px;
    .ava {
      width: 30px;
      height: 30px;
      background-color: #c3ecd9;
    }
    .avaPlayer {
      background-color: #fff;
    }
  }
  .dc-chat-cont {
    padding-left: 38px;
    .name-time {
      color: #91969d;
      font-size: 12px;
      margin-top: 2px;
      margin-bottom: 3px;
      .name {
        margin-right: 6px;
        margin-left: 4px;
      }
      .time {
        visibility: hidden;
      }
    }
    .chat-input-box {
      position: relative;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: space-around;
      padding: 4px 2px;
      border: 1px solid #c0c4cc;
      border-radius: 8px;
      background-color: #fff;
      .top-boxes {
        display: flex;
        width: 100%;
        justify-content: space-between;
        .tool-box {
          margin-right: 2px;
          line-height: 30px;
          position: relative;
          width: 27px;
          .tool-btn {
            border: none;
            font-size: 18px;
            margin: 0;
            height: 32px;
            position: absolute;
            bottom: 0;
            padding: 2px 5px;
            color: #606266;
          }
          .tool-btn:hover {
            background-color: inherit !important;
            color: #4aa181;
          }
          .emoji-btn {
            cursor: pointer;
            svg {
              margin-top: 6px;
            }
          }
        }
        .el-textarea {
          flex-grow: 1;
          width: 200px;
        }
        &:deep(#chat-input) {
          box-shadow: none !important;
          max-height: 40vh;
          resize: none;
          &:hover {
            box-shadow: none !important;
          }
        }
      }
    }
    .input-tips {
      margin-top: 5px;
      font-size: 12px;
      color: #a2a1a1;
      .pub-color {
        color: #4aa181;
        font-weight: 500;
      }
    }
    .dc-chat-content {
      width: 100%;
      position: relative;
      background-color: #ebeced;
      padding: 5px 10px;
      border-radius: 8px;
      display: inline-block;
      color: #23262a;
      font-size: 14px;
      .dc-reaction {
        margin: 0px 1px 1px;
        background: inherit;
        border: 0px;
        border-radius: 6px;
        cursor: default;
        user-select: none;
        &:deep(.joypixels) {
          width: 0.8rem;
          height: 0.8rem;
          display: inline-block;
          margin: 0px 1px;
          vertical-align: middle;
        }
      }
      .content-text {
        line-height: 1.375rem;
        font-size: 1rem;
        word-wrap: break-word;
        word-break: break-word;
        &:deep(p) {
          margin-block-start: 0.3em !important;
          margin-block-end: 0.3em !important;
        }
        &:deep(.joypixels) {
          width: 1rem;
          height: 1rem;
          display: inline-block;
          margin: 0px 1px;
          vertical-align: middle;
        }
      }
      .dc-tool {
        position: absolute;
        top: 10px;
        right: 0px;
        height: 1px;
        width: 1px;
        background-color: transparent;
        z-index: -1;
        visibility: hidden;
      }
    }
    .dc-chat-bot-content {
      background-color: #d4e3ff !important;
    }
    .has-edit {
      font-size: 12px;
      color: #91969d;
      margin-right: 5px;
    }
  }
  &:hover {
    .more {
      visibility: visible !important;
    }
  }
}
</style>
<style>
.custom-popper {
  width: auto !important;
  min-width: 20px !important;
  padding: 5px !important;
  background-color: transparent !important;
  border: 0 !important;
  box-shadow: none !important;
  .el-popper__arrow::before {
    width: 0;
    height: 0;
    border: none !important;
  }
}
</style>
