<template>
  <el-dialog
    v-model="value"
    width="600px"
    :title="Object.keys(props.editData).length > 0 ? $t('text_edit_tag') : $t('btn_add_tag')"
    :destroy-on-close="true"
    :before-close="close"
    :close-on-click-modal="false"
  >
    <el-form class="form" :model="form" label-position="top" ref="formRef" v-loading="loading">
      <el-form-item :label="$t('text_existing_tag')" style="font-weight: 700">
        <div v-if="form.label && form.label.length > 0">
          <el-tag
            v-for="(tag, index) in form.label"
            :key="index"
            size="small"
            closable
            @close="handleCloseTag(tag)"
            style="margin-right: 5px"
          >
            {{ tag.tag_desc }}
          </el-tag>
        </div>
        <span class="no-data" v-else>{{ $t('info_no_data') }}</span>
      </el-form-item>
      <!-- 新增标签 -->
      <el-form-item :label="$t('text_add_tag')">
        <el-cascader
          v-model="tagList"
          style="width: 100%"
          :options="discordTagData"
          filterable
          :placeholder="$t('text_tag_placeholder')"
          collapse-tags
          :reserve-keyword="false"
          collapse-tags-tooltip
          :max-collapse-tags="1"
          :props="{ multiple: true, emitPath: false, value: 'tag_id', label: 'tag_name' }"
          clearable
        >
        </el-cascader>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="close">{{ $t('btn_cancel') }}</el-button>
      <el-button type="primary" @click="submit">{{ $t('text_confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts">
import { useI18n } from 'vue-i18n';
import type { FormInstance } from 'element-plus';
import { addPortrait, getTagList } from '@/api/chatting';
import { linePortraitTag } from '@/api/line';
import { mailPortraitTag } from '@/api/mail';
export default defineComponent({
  name: 'EditTagLib',
  components: {
    ElMessage,
  },
});
interface EditTempLibProps {
  visible: boolean;
  editData?: Record<string, unknown>;
  paramsData?: Record<string, unknown>;
  mode?: string;
}
interface LabelObject {
  tag_id: number;
  tag_name: string;
}
interface Form {
  label: LabelObject[];
  tag?: string;
  id?: number;
  gender: number;
  birthday: string;
  career: string;
  education_level: number;
  married_state: number;
  fertility_state: number;
  remark: string;
  game_project: string;
}
</script>
<script setup lang="ts">
// base
const { t: $t } = useI18n();
const props = withDefaults(defineProps<EditTempLibProps>(), {
  visible: false,
  editData: () => ({}),
  paramsData: () => ({}),
  getGame: '',
});
const emit = defineEmits<{
  (event: 'update:visible', value: boolean): void;
  (event: 'success'): void;
}>();
const value = computed({
  get: () => props.visible,
  set: (val: boolean) => emit('update:visible', val),
});
const close = () => {
  value.value = false;
  emit('success');
};
const tagList = ref<LabelObject[]>([]);
const loading = ref(false);
const formRef = ref<FormInstance>();
const form = reactive<Form>({} as Form);
// 获取discord标签库
const discordTagData = ref<LabelObject[]>([]);
const getDiscordTag = async () => {
  try {
    let lib_type = props.paramsData.line_user_id ? 3 : 2;
    if (props.mode === 'mail') {
      lib_type = 4;
    }
    const res = await getTagList({ project_name: props.paramsData.game_project, lib_type });
    if (res && Array.isArray(res.data)) {
      discordTagData.value = res.data;
    } else {
      console.error('Invalid response:', res);
    }
  } catch (error) {
    console.error('Error discord lists:', error);
  }
};
getDiscordTag();

const submit = () => {
  formRef.value!.validate(async valid => {
    if (!valid || loading.value) return;
    loading.value = true;
    const labelIds = (form.label || []).map(item => item.tag_id || '');
    const params = {
      ...props.editData,
      ...form,
      label: [...new Set([...tagList.value, ...labelIds, ...hasTag.value])].join(','),
      ...props.paramsData,
    };
    props.editData.line_user_id && (params.tag = params.label);
    try {
      if (props.mode === 'mail') {
        await mailPortraitTag(params);
      } else {
        await (props.editData.line_user_id ? linePortraitTag(params) : addPortrait(params));
      }
      ElMessage.success($t('text_success'));
      emit('success');
      close();
    } catch (error) {
      console.log(error);
    } finally {
      loading.value = false;
    }
  });
};
const hasTag = ref<number[]>([]);
const handleCloseTag = (tag: LabelObject) => {
  const index = form.label.indexOf(tag);
  if (index !== -1) {
    form.label.splice(index, 1);
  }
  hasTag.value = form.label.map(item => item.tag_id);
};
// edit
onMounted(() => {
  if (Object.keys(props.editData).length > 0) {
    form.id = props.editData.id as number;
    form.label = (props.editData.new_label || props.editData.label) as LabelObject[];
    (form.gender = Number(props.editData.gender)),
      (form.birthday = props.editData.birthday as string),
      (form.career = props.editData.career as string),
      (form.education_level = Number(props.editData.education_level)),
      (form.married_state = Number(props.editData.married_state)),
      (form.fertility_state = Number(props.editData.fertility_state)),
      (form.remark = props.editData.remark as string),
      (form.game_project = (props.editData.game_project || props.editData.project) as string);
  }
});
</script>

<style lang="scss" scoped>
.no-data {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 14px;
  font-weight: normal;
  color: #909399;
}
</style>
