<template>
  <div v-if="props.stickerData.stickerResourceType === 'ANIMATION'">
    <img
      :src="`https://stickershop.line-scdn.net/stickershop/v1/sticker/${props.stickerData.stickerId}/android/sticker_animation.png`"
      alt="动态贴图"
    />
  </div>
  <div v-else-if="props.stickerData.stickerResourceType === 'STATIC'">
    <img
      :src="`https://stickershop.line-scdn.net/stickershop/v1/sticker/${props.stickerData.stickerId}/android/sticker.png`"
      alt="静态贴图"
    />
  </div>
  <div v-else>暂不支持的表情类型,请联系wenhao.wang</div>
</template>

<script setup lang="ts">
defineComponent({
  name: 'LineSticker',
});
interface StickerData {
  stickerId: string;
  stickerResourceType: string;
}
interface StickersReaderProps {
  stickerData: StickerData;
}
const props = defineProps<StickersReaderProps>();
</script>

<style lang="scss" scoped></style>
