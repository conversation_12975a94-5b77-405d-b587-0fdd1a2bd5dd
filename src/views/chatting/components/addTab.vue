<template>
  <el-dialog
    v-model="value"
    width="450px"
    :title="$t('text_fixed_tab')"
    :destroy-on-close="true"
    :before-close="close"
    :close-on-click-modal="false"
  >
    <el-form
      class="form"
      label-position="top"
      :model="form"
      :rules="rules"
      ref="formRef"
      v-loading="loading"
    >
      <!-- tab名称 -->
      <el-form-item :label="$t('text_tab_name')" prop="tab_name">
        <el-input
          type="text"
          :placeholder="$t('place_input')"
          v-model.trim="form.tab_name"
          maxlength="9"
          show-word-limit
        >
        </el-input>
      </el-form-item>
      <el-form-item :label="$t('text_isnot_all_people_look')">
        <el-radio-group v-model="form.public" prop="public" @change="handleChange">
          <el-radio :value="1" v-has="'addTab:isAll'">{{ $t('text_yes') }}</el-radio>
          <el-radio :value="2">{{ $t('text_no') }}</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="close">{{ $t('btn_cancel') }}</el-button>
      <el-button type="primary" @click="submit">{{ $t('text_confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts">
import { useI18n } from 'vue-i18n';
import type { FormInstance, FormRules } from 'element-plus';
import { addDiscordTab, editDiscordTab } from '@/api/chatting';
import { addLineTab, editLineTab } from '@/api/line';
import { addMailTab, editMailTab } from '@/api/mail';
export default defineComponent({
  name: 'EditTagLib',
  components: {
    ElMessage,
  },
});
interface EditTabProps {
  visible: boolean;
  paramsData?: Record<string, unknown>;
  mode?: string;
}
interface Form {
  tab_name: string;
  public: number;
}
</script>
<script setup lang="ts">
// base
const { t: $t } = useI18n();
const props = withDefaults(defineProps<EditTabProps>(), {
  visible: false,
  paramsData: () => ({}),
});
const emit = defineEmits<{
  (event: 'update:visible', value: boolean): void;
  (event: 'success'): void;
}>();
const value = computed({
  get: () => props.visible,
  set: (val: boolean) => emit('update:visible', val),
});
const close = () => {
  value.value = false;
};
const loading = ref(false);
const formRef = ref<FormInstance>();
const form = reactive<Form>({
  tab_name: '',
  public: 2,
});
const rules = reactive<FormRules>({
  tab_name: [{ required: true, message: $t('place_input'), trigger: 'blur' }],
});
const handleChange = (val: number) => {
  form.public = val;
};
const submit = () => {
  formRef.value!.validate(valid => {
    if (!valid || loading.value) return;
    loading.value = true;
    // 区分是Discord还是Line 的 Tab
    // 如果props.paramsData中有一个key为line_user_id，则是Line
    let request;
    if (Object.prototype.hasOwnProperty.call(props.paramsData, 'line_user_id')) {
      request = props.paramsData.id
        ? editLineTab({ id: props.paramsData.id, ...form })
        : addLineTab({ ...form, detail: props.paramsData });
    } else if (props.mode === 'mail') {
      request = props.paramsData.id
        ? editMailTab({ id: props.paramsData.id, ...form })
        : addMailTab({ ...form, detail: props.paramsData });
    } else {
      request = props.paramsData.id
        ? editDiscordTab({ id: props.paramsData.id, ...form })
        : addDiscordTab({ ...form, detail: props.paramsData });
    }
    request
      .then(() => {
        ElMessage.success($t('text_success'));
        emit('success');
        close();
      })
      .finally(() => {
        loading.value = false;
      });
  });
};
// 编辑时
onMounted(() => {
  if (props.paramsData.tab_name) {
    form.tab_name = props.paramsData.tab_name as string;
    form.public = props.paramsData.public as number;
  }
});
</script>

<style scoped>
.upload_box {
  width: 100%;
}
</style>
