<template>
  <el-dialog
    v-model="visible"
    width="800px"
    :title="$t('btn_add_remark')"
    :destroy-on-close="true"
    :close-on-click-modal="false"
    :before-close="handleClose"
  >
    <el-form :model="form" label-width="80px">
      <el-form-item :label="$t('text_remark')">
        <opsEditer v-model="form.remark" v-model:uploading="isUploading"></opsEditer>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">{{ $t('btn_cancel') }}</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">
          {{ $t('text_confirm') }}
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue';
import { ElMessage } from 'element-plus';
import { useI18n } from 'vue-i18n';
// import { updateEmailRemark } from '@/api/mail'; // 需要添加相应的API

const { t: $t } = useI18n();

interface Props {
  visible: boolean;
  emailId: string | number;
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  emailId: '',
});

const emit = defineEmits<{
  'update:visible': [value: boolean];
  success: [];
}>();

const visible = computed({
  get: () => props.visible,
  set: (val: boolean) => emit('update:visible', val),
});

const isUploading = ref(false);
const loading = ref(false);
const form = reactive({
  remark: '',
});

const handleClose = () => {
  visible.value = false;
  form.remark = '';
};

const handleSubmit = async () => {
  if (!form.remark.trim()) {
    ElMessage.warning($t('text_please_input_remark'));
    return;
  }

  loading.value = true;
  try {
    // 这里需要调用实际的API
    // await updateEmailRemark({
    //   email_id: props.emailId,
    //   remark: form.remark,
    // });

    // 临时模拟成功
    await new Promise(resolve => setTimeout(resolve, 1000));

    ElMessage.success($t('text_success'));
    emit('success');
    handleClose();
  } catch (error) {
    console.error('更新备注失败:', error);
    ElMessage.error($t('text_operation_failed'));
  } finally {
    loading.value = false;
  }
};
</script>
