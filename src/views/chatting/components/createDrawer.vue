<template>
  <el-drawer
    v-model="value"
    :title="$t('text_create_communication_records')"
    :before-close="close"
    direction="rtl"
  >
    <el-form
      class="form"
      label-position="top"
      :model="form"
      :rules="rules"
      ref="formRef"
      v-loading="loading"
    >
      <!-- 日期 -->
      <el-form-item :label="$t('text_time')" prop="commu_date">
        <el-date-picker
          v-model="form.commu_date"
          type="date"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          :placeholder="$t('place_select')"
        />
      </el-form-item>
      <!-- 沟通问题 -->
      <el-form-item :label="$t('text_communication_problem')" prop="question">
        <el-input v-model="form.question" clearable :placeholder="$t('place_input')" />
      </el-form-item>
      <!-- 问题类型 -->
      <el-form-item :label="$t('text_qtype')" prop="cat_id">
        <el-cascader
          v-model="form.cat_id"
          style="width: 100%"
          :options="questionList"
          filterable
          :placeholder="$t('text_tag_placeholder')"
          collapse-tags
          :reserve-keyword="false"
          collapse-tags-tooltip
          :max-collapse-tags="1"
          :props="{ checkStrictly: true, emitPath: false, value: 'id', label: 'label' }"
          clearable
        >
        </el-cascader>
      </el-form-item>
      <!-- 处理状态 -->
      <el-form-item :label="$t('text_process_status')" prop="handle_status">
        <el-select v-model="form.handle_status" :placeholder="$t('place_select')" clearable>
          <el-option
            v-for="(v, index) in enumList.QuestionHandleStatus"
            :key="index"
            :label="v.name"
            :value="v.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <!-- 备注 -->
      <el-form-item :label="$t('text_remark')" prop="remark">
        <opsEditer v-model="form.remark" v-model:uploading="isUploading"></opsEditer>
      </el-form-item>
    </el-form>
    <div class="drawer_footer">
      <el-button @click="close">{{ $t('btn_cancel') }}</el-button>
      <el-button type="primary" @click="submit">{{ $t('text_confirm') }}</el-button>
    </div>
  </el-drawer>
</template>

<script lang="ts">
import { useI18n } from 'vue-i18n';
import type { FormInstance, FormRules } from 'element-plus';
import { useEnumStore } from '@/stores';
import { addCommunicate } from '@/api/chatting';
import { lineAddCommunicate } from '@/api/line';
import { questionTypeList } from '@/api/report';
export default defineComponent({
  name: 'CreateDrawer',
  components: {
    ElMessage,
  },
});
interface EditTempLibProps {
  visible: boolean;
  paramsData?: Record<string, unknown>;
  msgIds: string;
}
interface Form {
  commu_date: string;
  question: string;
  cat_id: number;
  handle_status: string;
  remark: string;
}
</script>
<script setup lang="ts">
// base
const { t: $t } = useI18n();
const props = withDefaults(defineProps<EditTempLibProps>(), {
  visible: false,
  msgIds: '',
  paramsData: () => ({}),
});
const emit = defineEmits<{
  (event: 'update:visible', value: boolean): void;
  (event: 'success'): void;
}>();
const value = computed({
  get: () => props.visible,
  set: (val: boolean) => emit('update:visible', val),
});
const close = () => {
  value.value = false;
};

const enumList = computed(() => useEnumStore().enumList);
const loading = ref(false);
const isUploading = ref(false);
const questionList = ref([]);
const formRef = ref<FormInstance>();
const form = reactive<Form>({} as Form);
const rules = reactive<FormRules>({
  commu_date: [{ required: true, message: $t('place_select'), trigger: 'change' }],
  question: [{ required: true, message: $t('place_input'), trigger: 'blur' }],
  cat_id: [{ required: true, message: $t('place_select'), trigger: 'change' }],
  handle_status: [{ required: true, message: $t('place_select'), trigger: 'change' }],
});
const submit = () => {
  formRef.value!.validate(async valid => {
    if (!valid || loading.value) return;
    loading.value = true;
    try {
      const { project, uid, sid, user_name, total_pay, dsc_user_id, line_user_id, display_name } =
        props.paramsData;
      await (props.paramsData.line_user_id
        ? lineAddCommunicate({
            ...form,
            project,
            uid,
            sid,
            nick_name: display_name,
            pay_all: total_pay,
            line_user_id,
            msg_ids: props.msgIds,
            cat_type: 2,
          })
        : addCommunicate({
            ...form,
            project,
            uid,
            sid,
            nick_name: user_name,
            pay_all: total_pay,
            dsc_user_id,
            msg_ids: props.msgIds,
            cat_type: 1,
          }));
      ElMessage.success($t('text_success'));
      emit('success');
      close();
    } catch (error) {
      console.log(error);
    } finally {
      loading.value = false;
    }
  });
};
onMounted(() => {
  form.commu_date = new Date().toISOString().substring(0, 10);
  // 获取问题类型 dc是1，line是2
  questionTypeList({
    project: props.paramsData.project,
    cat_type: props.paramsData.line_user_id ? 2 : 1,
  })
    .then((res: any) => {
      questionList.value = res.data;
    })
    .finally(() => {});
});
</script>

<style lang="scss" scoped>
.drawer_footer {
  position: absolute;
  bottom: 10px;
  right: 10px;
}
</style>
