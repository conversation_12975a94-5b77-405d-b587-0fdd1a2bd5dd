<template>
  <el-dialog
    v-model="value"
    width="600px"
    :title="isBatch ? $t('btn_batch_assign') : $t('text_assign')"
    :destroy-on-close="true"
    :before-close="close"
    :close-on-click-modal="false"
  >
    <el-form
      class="form"
      label-position="top"
      :model="form"
      :rules="rules"
      ref="formRef"
      v-loading="loading"
    >
      <!-- <el-form-item v-if="isBatch" label="选中邮件数量">
        <el-tag type="info">已选择 {{ emailIds.length }} 封邮件</el-tag>
      </el-form-item> -->
      <el-form-item :label="$t('text_user_value')" prop="acceptor">
        <el-select
          v-model="form.acceptor"
          :placeholder="$t('place_select')"
          :reserve-keyword="false"
          filterable
          clearable
        >
          <el-option
            v-for="(v, index) in csList"
            :key="index"
            :label="v.account"
            :value="v.account"
          ></el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="close">{{ $t('btn_cancel') }}</el-button>
      <el-button type="primary" @click="submit">{{ $t('text_confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts">
import { defineComponent, ref, reactive, computed, onMounted } from 'vue';
import { useI18n } from 'vue-i18n';
import type { FormInstance, FormRules } from 'element-plus';
import { ElMessage } from 'element-plus';
import { useUserInfoStore } from '@/stores';
import { assignEmail, batchAssignEmail } from '@/api/mail';
import { getAcceptorList } from '@/api/assignConfig';
export default defineComponent({
  name: 'MailAssign',
  components: {
    ElMessage,
  },
});
interface AssignProps {
  visible: boolean;
  emailId?: number | string;
  emailIds?: (number | string)[];
}
interface Form {
  email_id?: number | string;
  email_ids?: (number | string)[];
  acceptor: string;
}
</script>
<script setup lang="ts">
// base
const { t: $t } = useI18n();
const props = withDefaults(defineProps<AssignProps>(), {
  visible: false,
  emailId: 0,
  emailIds: () => [],
});
const emit = defineEmits<{
  (event: 'update:visible', value: boolean): void;
  (event: 'success'): void;
}>();

// 判断是否为批量操作
const isBatch = computed(() => {
  return props.emailIds && props.emailIds.length > 0;
});

const value = computed({
  get: () => props.visible,
  set: (val: boolean) => emit('update:visible', val),
});
const close = () => {
  value.value = false;
};

// business
const csList = ref([]);
const getCsList = async () => {
  const res = await getAcceptorList({});
  csList.value = res;
};
getCsList();

const userInfo = computed(() => useUserInfoStore().userInfo as Record<string, unknown>);
const loading = ref(false);
const formRef = ref<FormInstance>();
const form = reactive<Form>({
  acceptor: userInfo.value.username as string,
});
const rules = reactive<FormRules>({
  acceptor: [{ required: true, message: $t('place_select'), trigger: 'change' }],
});

const submit = () => {
  formRef.value!.validate(async valid => {
    if (!valid || loading.value) return;
    loading.value = true;
    try {
      if (isBatch.value) {
        // 批量指派
        await batchAssignEmail(form);
      } else {
        // 单个指派
        await assignEmail(form);
      }
      ElMessage.success($t('text_success'));
      emit('success');
      close();
    } catch (error) {
      console.log(error);
    } finally {
      loading.value = false;
    }
  });
};

// edit
onMounted(() => {
  if (isBatch.value) {
    form.email_ids = props.emailIds;
  } else {
    form.email_id = props.emailId;
  }
});
</script>
