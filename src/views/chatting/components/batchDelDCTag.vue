<template>
  <el-dialog
    v-model="value"
    width="600px"
    :title="$t('text_batch_del_tag')"
    :destroy-on-close="true"
    :before-close="close"
    :close-on-click-modal="false"
  >
    <el-form class="form" label-position="top" ref="formRef" v-loading="loading">
      <el-form-item :label="$t('text_public_label')">
        <div v-if="tagOpts.length > 0">
          <el-tag
            v-for="tag in tagOpts"
            :key="tag.tag_id"
            closable
            @close="delTag(tag.tag_id)"
            style="margin: 2px"
          >
            {{ tag.tag_name }}
          </el-tag>
        </div>
        <div style="text-align: center; width: 100%" v-else>{{ $t('info_no_data') }}</div>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="close">{{ $t('btn_cancel') }}</el-button>
      <el-button type="primary" @click="submit">{{ $t('text_confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts">
import { useI18n } from 'vue-i18n';
import type { FormInstance } from 'element-plus';
import { getDcCommonTag, batchDeleteDCTag } from '@/api/chatting';
export default defineComponent({
  name: 'BatchDelDCTag',
  components: {
    ElMessage,
  },
});
interface BatchDelTagProps {
  visible: boolean;
  batchIds: string[];
  paramsData?: Record<string, unknown>;
}
</script>
<script setup lang="ts">
// base
const { t: $t } = useI18n();
const props = withDefaults(defineProps<BatchDelTagProps>(), {
  visible: false,
  paramsData: () => ({}),
  batchIds: () => [],
});
const emit = defineEmits<{
  (event: 'update:visible', value: boolean): void;
  (event: 'success'): void;
}>();
const value = computed({
  get: () => props.visible,
  set: (val: boolean) => emit('update:visible', val),
});
const close = () => {
  value.value = false;
};
const loading = ref(false);
const formRef = ref<FormInstance>();
const tagOpts = ref<any[]>([]);
const delTagIds = ref<number[]>([]);
const cacheParamsData = ref<any>({});
const cacheBatchIds = ref<string[]>([]);

const delTag = (tagId: number) => {
  const index = tagOpts.value.findIndex(tag => tag.tag_id === tagId);
  tagOpts.value.splice(index, 1);
  delTagIds.value.push(tagId);
};
const submit = () => {
  batchDeleteDCTag({
    dsc_user_id_list: cacheBatchIds.value,
    tag_ids: delTagIds.value,
    project: cacheParamsData.value.project,
  }).then(() => {
    ElMessage.success($t('text_del_success'));
    value.value = false;
    emit('success');
  });
};
// 获取公共的标签
const getTags = async () => {
  loading.value = true;
  getDcCommonTag({
    dsc_user_id_list: cacheBatchIds.value,
  })
    .then((res: any) => {
      tagOpts.value = res.tags;
    })
    .finally(() => {
      loading.value = false;
    });
};

onMounted(() => {
  // 缓存参数
  cacheParamsData.value = JSON.parse(JSON.stringify(props.paramsData));
  cacheBatchIds.value = JSON.parse(JSON.stringify(props.batchIds));
  if (cacheBatchIds.value.length > 0) {
    getTags();
  }
});
</script>

<style scoped></style>
