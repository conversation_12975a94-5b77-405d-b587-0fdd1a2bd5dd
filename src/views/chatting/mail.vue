<template>
  <splitpanes class="split-box default-theme" vertical :push-other-panes="false">
    <!-- 左侧内容区域 -->
    <pane size="13.5">
      <splitpanes class="split-box o-left default-theme" horizontal :push-other-panes="false">
        <!-- 数据概览 -->
        <pane>
          <DataOverview
            :overview-data="overviewData"
            :active-shortcut-id="activeShortcutId"
            @shortcut-click="shortcutHandle"
          />
        </pane>
        <!-- 自定义tab数据概览 -->
        <pane>
          <CustomTabOverview
            :tab-list="tabList"
            :tabs-loading="tabsLoading"
            :open-index="openIndex"
            :active-tab-id="activeTabId"
            :user-info="userInfo"
            @tab-click="handleClickTab"
            @tab-edit="handleEditTab"
            @tab-delete="handleDelete"
          />
        </pane>
      </splitpanes>
    </pane>
    <!-- 中间内容区域 -->
    <pane>
      <splitpanes class="split-box default-theme" horizontal :push-other-panes="false">
        <pane size="25">
          <SearchForm
            v-model:search-form="searchForm"
            :email-list="emailList"
            :email-status-list="emailStatusList"
            :email-tags-list="emailTagsList"
            :cs-list="csList"
            :prog-state="progState"
            :progress-num="progressNum"
            :check-email-list="checkEmailList"
            @search="search"
            @reset="reset"
            @download="downloadData"
            @add-tab="handleAddTabData"
            @batch-assign="() => handleAssignEmail({}, true)"
          />
        </pane>
        <pane>
          <EmailList
            v-model:chat-list-data="chatListData"
            v-model:check-all="checkAll"
            v-model:order-by="orderBy"
            v-model:pool-sort-type="poolSortType"
            :pool-loading="poolLoading"
            :email-sort-options="emailSortOptions"
            @email-click="activeChatHandle"
            @check-change="handleCheckItemChange"
            @all-select-change="onAllSelectChange"
            @assign-email="handleAssignEmail"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </pane>
      </splitpanes>
    </pane>
    <!-- 右侧内容区域 -->
    <pane size="40" style="min-width: 410px">
      <EmailDetail
        :active-email-info="activeEmailInfo"
        :reply-form="replyForm"
        :reply-loading="replyLoading"
        :is-uploading="isUploading"
        @reply="replyHandle"
        @remark="remarkHandle"
        @edit-tag="editTagHandle"
        @download-attachment="downloadAttachment"
        @update:reply-form="value => (replyForm = value)"
        @update:is-uploading="value => (isUploading = value)"
      />
    </pane>
    <add-tag
      v-if="tagEditVisible"
      v-model:visible="tagEditVisible"
      :edit-data="tagData"
      :params-data="paramsData"
      @success="searchHandle"
    ></add-tag>
    <batch-message
      v-if="showBatchBox"
      v-model:visible="showBatchBox"
      :ticket-ids="uniqueArr"
      :params-data="messageData"
      @success="search"
    ></batch-message>
    <add-tab
      v-if="tabVisible"
      v-model:visible="tabVisible"
      :params-data="tabData"
      @success="getTabList"
    ></add-tab>
    <patch-tag
      v-if="patchTagVisible"
      v-model:visible="patchTagVisible"
      :user-ids="checkUserIdList"
      :params-data="projectData"
      @success="search"
    ></patch-tag>
    <!-- 批量删除标签组件 -->
    <batch-deltag
      v-if="batchDelTagVisible"
      v-model:visible="batchDelTagVisible"
      :params-data="projectData"
      :batch-ids="checkUserIdList"
      @success="search"
    />
    <!-- 邮件指派组件 -->
    <mailAssign
      v-if="assignDialogVisible"
      v-model:visible="assignDialogVisible"
      :email-id="assignEmailId"
      :email-ids="checkEmailList"
      @success="searchSuccess"
    />
    <!-- 备注组件 -->
    <mailRemark
      v-if="remarkVisible"
      v-model:visible="remarkVisible"
      :email-id="activeEmailInfo.thread_id || activeEmailInfo.id"
      @success="searchSuccess"
    />
    <!-- 标签编辑组件 -->
    <mailEditTag
      v-if="editTagVisible"
      v-model:visible="editTagVisible"
      :email-id="activeEmailInfo.thread_id || activeEmailInfo.id"
      @success="searchSuccess"
    />
  </splitpanes>
</template>

<script lang="ts">
import type { FormInstance } from 'element-plus';
import { Splitpanes, Pane } from 'splitpanes';
import 'splitpanes/dist/splitpanes.css';
import { useEnumStore, useUserInfoStore, useAppStore } from '@/stores';
import { getAcceptorList } from '@/api/assignConfig';
import {
  getStats,
  getPortrait,
  discordDown,
  addPortrait,
  addMark,
  fetchTabLists,
  deleteTab,
  discordTagList,
  robotList,
  discordReplyStatus,
  getTagList,
  getTabCount,
  updateTabSettingOrder,
} from '@/api/chatting';
import { getThreadsList, getThreadDetail, replyThreadMessage, markThreadAsRead } from '@/api/mail';
import opsDcChat from './components/opsDcChat.vue';
import addTag from './components/addTag.vue';
import batchMessage from './components/batchMessage.vue';
import batchDeltag from './components/batchDelDCTag.vue';
import addTab from './components/addTab.vue';
import patchTag from './components/pathAddTag.vue';
import mailAssign from './components/mailAssign.vue';
import mailRemark from './components/mailRemark.vue';
import mailEditTag from './components/mailEditTag.vue';
import DragIcon from '@/components/DragIcon.vue';
// 新的拆分组件
import DataOverview from './mail/components/DataOverview.vue';
import CustomTabOverview from './mail/components/CustomTabOverview.vue';
import SearchForm from './mail/components/SearchForm.vue';
import EmailList from './mail/components/EmailList.vue';
import EmailDetail from './mail/components/EmailDetail.vue';
const userInfo = computed(() => useUserInfoStore().userInfo as Record<string, unknown>);
export default defineComponent({
  name: 'Mail',
  components: {
    Splitpanes,
    Pane,
    opsDcChat,
    addTag,
    batchMessage,
    addTab,
    patchTag,
    batchDeltag,
    mailAssign,
    mailRemark,
    mailEditTag,
    DragIcon,
    // 新的拆分组件
    DataOverview,
    CustomTabOverview,
    SearchForm,
    EmailList,
    EmailDetail,
  },
});
interface SearchForm {
  // 邮件相关字段
  email_id?: number;
  page?: number;
  page_size?: number;
  keyword?: string;
  sender_email?: string;
  receiver_email?: string;
  status?: string;
  has_unread?: boolean;
  has_attachments?: boolean;
  priority?: number;
  start_time?: string;
  end_time?: string;
  created_start_time?: string;
  created_end_time?: string;
  thread_id?: string;
  tags?: number[];
  processor?: string[];
  remark_search?: string;
  emails?: string[];
  email_tags?: string[];
  email_processor?: string[];

  // 保留原有字段以兼容
  project?: string[];
  replied_at?: Array<string>;
  last_login?: Array<string>;
  pay_all_prev?: number;
  pay_all_next?: number;
  pay_prev?: number;
  pay_next?: number;
  vip_state?: number;
  tag?: string[];
  dsc_user_nickname?: string;
  bot_ids?: string[];
  user_content?: string;
  user_detail_remark?: string;
  dsc_user_id?: string;
  uids?: number;
  fpid?: string;
  sid?: string;
  bot_id?: string;
  note?: string;
  checked?: boolean;
  birthday?: string;
  language?: string[];
  dm_channel?: string;
  pay_last_thirty_days?: number[] | undefined;
  pay_all?: number[] | undefined;
  reply_type?: number | undefined;
  last_reply_service?: string[];
  tag_type?: number | null;
  tab_name?: string;
  id?: number;
  public?: number;
}
interface LabelObject {
  tag_id: number;
  tag_name: string;
}
interface BaseForm {
  tag: number[];
  id: number;
  dsc_user_id: string;
  fpid: string;
  game_project: string;
  tags?: LabelObject[];
  gender?: number;
  birthday: string;
  career: string;
  education_level?: number;
  married_state?: number;
  fertility_state?: number;
  remark: string;
}
type SearchFormKey = keyof SearchForm;
type SearchFormValue = SearchForm[keyof SearchForm];
interface shortcutButtonT {
  id: number;
  label: string;
  shortcut: Record<string, unknown>;
}
</script>
<script setup lang="ts">
import {
  ref,
  reactive,
  computed,
  watch,
  onMounted,
  onUnmounted,
  provide,
  nextTick,
  defineComponent,
} from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useI18n } from 'vue-i18n';
import { ElMessage, ElMessageBox } from 'element-plus';
import type { CheckboxValueType } from 'element-plus';
import Sortable from 'sortablejs';

const router = useRouter();
const { t: $t } = useI18n();
const csList = ref<Record<string, string>[]>([]);
const isEditMode = ref<boolean>(false);
const isRemarkEditMode = ref<boolean>(false);
const portraitLoading = ref(false);
const tagEditVisible = ref(false);
const tabVisible = ref(false);
const patchTagVisible = ref(false);
const tabsLoading = ref(false);
const tagData = ref({});
const paramsData = ref({});
const messageData = ref({});
const tabData = ref<SearchForm>({} as SearchForm);
const projectData = ref({});
const orderBy = ref('');
const poolSortType = ref();
const baseBtnText = ref<string>($t('btn_edit'));
const remarkBtnText = ref<string>($t('btn_edit'));
const tagLists = ref<Array<{ id: number; tag_name: string }>>([]);
const isTimerActive = ref(true); // 表示定时任务应该运行
const tabList = ref<any[]>([]);
const openIndex = ref<string[]>([]);
const discordTagData = ref<Array<{ id: number; tag_name: string }>>([]);
const checkAll = ref(false);
const checkDataList = ref<number[]>([]);
const checkUserIdList = ref<string[]>([]);
const checkProject = ref<string[]>([]);
const checkBotId = ref<string[]>([]);

// 邮件指派相关变量
const assignVisible = ref(false);
const batchAssignVisible = ref(false);
const assignEmailId = ref<number | string>(0);
const checkEmailList = ref<(number | string)[]>([]);

// 统一的指派弹窗显示状态
const assignDialogVisible = computed({
  get: () => assignVisible.value || batchAssignVisible.value,
  set: (val: boolean) => {
    if (!val) {
      assignVisible.value = false;
      batchAssignVisible.value = false;
    }
  },
});

// 邮件相关数据源
const emailList = ref<Array<{ value: string; label: string }>>([]);
const emailStatusList = ref([
  { value: 1, label: 'text_pending_assign' }, // 待分配
  { value: 2, label: 'text_processing' }, // 处理中
  { value: 3, label: 'text_completed' }, // 已完成
  { value: 4, label: 'text_rejected' }, // 已拒单
]);
const emailTagsList = ref<Array<{ value: string; label: string }>>([]);

// 邮件排序选项
const emailSortOptions = ref([
  { value: 'created_at', name: 'text_create_time_sort' }, // 按创建时间排序
  { value: 'last_message_at', name: 'text_wait_time_sort' }, // 按等待时间排序（最后消息时间）
]);

// 当前选中的邮件详情
const activeEmailInfo = ref<any>({});
const replyForm = ref({
  reply_type: 'reply',
  reply_body: '',
});
const replyLoading = ref(false);

const cascaderProps = {
  multiple: true,
  emitPath: false,
  value: 'tag_id',
  label: 'tag_name',
};
// 按钮携带下载进度
const progressNum = computed(() => useAppStore().progressNum);
const progState = ref<boolean>(false);
watch(progressNum, n => {
  progState.value = n < 100 && n > -1 ? true : false;
});
const colorsMap = {
  1: '#E6A23C', // 待处理-黄色
};
const getCsList = async () => {
  const res = await getAcceptorList({});
  csList.value = res;
};
getCsList();

// 获取邮箱列表
const getEmailList = async () => {
  try {
    // 这里应该调用实际的API获取邮箱列表
    // const res = await getEmailList({})
    // emailList.value = res.data

    // 临时模拟数据
    emailList.value = [
      { value: '<EMAIL>', label: '<EMAIL>' },
      { value: '<EMAIL>', label: '<EMAIL>' },
      { value: '<EMAIL>', label: '<EMAIL>' },
    ];
  } catch (error) {
    console.log('获取邮箱列表失败:', error);
  }
};
getEmailList();

// 获取邮件标签列表
const getEmailTagsList = async () => {
  try {
    // 这里应该调用实际的API获取邮件标签列表
    // const res = await getEmailTagsList({})
    // emailTagsList.value = res.data

    // 临时模拟数据
    emailTagsList.value = [
      { value: 'urgent', label: '紧急' },
      { value: 'complaint', label: '投诉' },
      { value: 'suggestion', label: '建议' },
      { value: 'bug', label: 'Bug反馈' },
    ];
  } catch (error) {
    console.log('获取邮件标签列表失败:', error);
  }
};
getEmailTagsList();

// 格式化发送日期
const formatSendDate = (dateStr: string) => {
  if (!dateStr) return '-';
  const date = new Date(dateStr);
  const month = date.getMonth() + 1;
  const day = date.getDate();
  return `${month}月${day}日`;
};

// 格式化完整日期时间- utc0时区
const formatDateTime = (dateStr: string) => {
  if (!dateStr) return '-';
  const date = new Date(dateStr);
  return date.toLocaleString('zh-CN', { timeZone: 'UTC' });
};

// 格式化文件大小
const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// 获取状态类型
const getStatusType = (status: string) => {
  switch (status) {
    case 'active':
      return 'success';
    case 'closed':
      return 'info';
    case 'archived':
      return 'warning';
    default:
      return 'info';
  }
};

// 获取状态文本
const getStatusText = (status: string) => {
  switch (status) {
    case 'active':
      return '活跃';
    case 'closed':
      return '已关闭';
    case 'archived':
      return '已归档';
    default:
      return '未知';
  }
};

// 处理邮件指派
const handleAssignEmail = (email: any, isBatch: boolean = false) => {
  if (isBatch) {
    batchAssignVisible.value = true;
  } else {
    assignVisible.value = true;
    assignEmailId.value = email.id || email.thread_id;
  }
};

// 指派成功回调
const searchSuccess = () => {
  search();
  getOverview();
  // 如果有选中的邮件详情，重新加载
  if (activeEmailInfo.value.thread_id) {
    loadEmailDetail(activeEmailInfo.value.thread_id);
  }
};

// 备注处理
const remarkVisible = ref(false);
const remarkHandle = () => {
  remarkVisible.value = true;
};

// 标签编辑处理
const editTagVisible = ref(false);
const editTagHandle = () => {
  editTagVisible.value = true;
};

// 判断是否不可操作 - todo
const noPermission = computed(() => {
  return activeEmailInfo.value.last_reply_service === userInfo.value.username;
});

// 下载附件
const downloadAttachment = (attachment: any) => {
  if (attachment.size > 50 * 1024 * 1024) {
    ElMessage.warning('文件大小超过50MB，无法下载');
    return;
  }
  // 这里应该调用下载API
  window.open(attachment.file_url, '_blank');
};

// 回复处理 (1: 回复, 2: 回复关闭, 3: 拒单关闭)
const replyHandle = async (op_type: number) => {
  if (!replyForm.value.reply_body.trim() && op_type !== 3) {
    ElMessage.warning('请输入回复内容');
    return;
  }

  replyLoading.value = true;
  try {
    await replyThreadMessage({
      email_id: activeEmailInfo.value.email_id,
      thread_id: activeEmailInfo.value.thread_id,
      reply_body: replyForm.value.reply_body,
      op_type: op_type,
    });
    ElMessage.success('操作成功');
    clearReply();
    // 重新加载邮件详情和列表
    await loadEmailDetail();
    searchSuccess();
  } catch (error) {
    console.error('操作失败:', error);
    ElMessage.error('操作失败');
  } finally {
    replyLoading.value = false;
  }
};

// 清空回复
const clearReply = () => {
  replyForm.value.reply_body = '';
  replyForm.value.reply_type = 'reply';
};

// 加载邮件详情
const loadEmailDetail = async () => {
  try {
    const res = await getThreadDetail({
      email_id: activeEmailInfo.value.email_id,
      thread_id: activeEmailInfo.value.thread_id,
    });
    console.log('res', res);

    const response = res.data;

    if (response.code === 0 || response.code === 200) {
      activeEmailInfo.value = response.data;
      console.log('activeEmailInfo.value', activeEmailInfo.value);
      // // 标记为已读
      // await markThreadAsRead({
      //   email_id: activeEmailInfo.value.email_id,
      //   thread_id: activeEmailInfo.value.thread_id,
      // });
    }
  } catch (error) {
    console.error('加载邮件详情失败:', error);
  }
};

// 获取固定tab列表
const getTabList = async () => {
  tabsLoading.value = true;
  tabList.value = [];
  try {
    const tabLists = await fetchTabLists({});
    const tabCounts = await getTabCount({});
    if (tabLists.data.length === 0) {
      tabsLoading.value = false;
      return;
    }
    tabLists.data.forEach((item1: any) => {
      const projectName = item1.project;
      const newTabList = [] as any;
      openIndex.value.push(projectName); // 默认展开所有tab菜单项
      item1.tab.forEach((tab1: any) => {
        // 在tabCounts中寻找对应的项目
        const matchingProject = tabCounts.detail.find(
          (item2: any) => item2.project === projectName
        );
        if (matchingProject) {
          // 找到匹配的项目后，查找对应的tab_name
          const matchingTab = matchingProject.tab.find(
            (tab2: any) => tab2.tab_name === tab1.tab_name
          );
          if (matchingTab) {
            // 如果匹配成功，输出对应的值
            newTabList.push({
              ...tab1,
              count: matchingTab.count,
            });
          }
        }
      });
      // 组成新的数据结构
      tabList.value.push({
        project: projectName,
        tabs: newTabList,
      });
      tabsLoading.value = false;
    });
  } catch (error) {
    console.log(error);
    tabsLoading.value = false;
  }
};
getTabList();
// 获取discord标签库
const getDiscordTag = () => {
  discordTagList({ status: 1 }).then((res: any) => {
    if (res && Array.isArray(res.data)) {
      discordTagData.value = res.data;
    }
  });
};
getDiscordTag();
const searchFormInitData = {
  // 邮件相关字段初始值
  email_id: undefined, // 默认邮箱ID
  page: 1,
  page_size: 20,
  keyword: '',
  sender_email: '',
  receiver_email: '',
  status: '',
  has_unread: undefined,
  has_attachments: undefined,
  priority: undefined,
  start_time: '',
  end_time: '',
  created_start_time: '',
  created_end_time: '',
  thread_id: '',
  tags: [],
  processor: [],
  remark_search: '',

  // 保留原有字段
  project: [],
  replied_at: [],
  last_login: [],
  tag: [],
  user_detail_remark: '',
  user_content: '',
  dsc_user_nickname: '',
  bot_ids: [],
  dsc_user_id: '',
  fpid: '',
  sid: '',
  bot_id: '',
  reply_type: undefined,
  last_reply_service: [],
  tag_type: null,
  tab_name: '',
  id: undefined,
  public: undefined,
};
// 快捷查询按钮
const ovButtonList = [
  {
    id: 1,
    label: 'text_all_mail', // 所有邮箱 - 展示全部接入邮箱收到的邮件
    shortcut: searchFormInitData,
    prop: 'discord_user_count',
  },
  {
    id: 2,
    label: 'text_pending_reply_message_email', // 待回复邮件 - 未回复的邮件
    shortcut: {
      status: [1, 2],
    },
    prop: 'wait_reply_accounts',
  },
  {
    id: 3,
    label: 'text_mine_pending_reply_email', // 我的待回复邮件 - 处理人=当前登录人且未回复的邮件
    shortcut: {
      status: [1, 2],
      reply_type: 1,
      last_reply_service: 'CurrentUser',
    },
    prop: 'mine_wait_reply_accounts',
  },
];
const overviewData: Record<string, number> = reactive({
  discord_user_count: 0,
  wait_reply_accounts: 0,
  mine_wait_reply_accounts: 0,
});
const tagOpts = ref<any[]>([]);
const changeGameHandle = (val: string[]) => {
  searchForm.value.tags = [];
  tagOpts.value = [];
  if (val.length === 1 && val[0]) {
    getTagList({ project_name: val[0] ? val[0] : '', lib_type: 2 }).then((res: any) => {
      if (res && res.data) {
        tagOpts.value = res.data;
        tagOpts.value.unshift({ tag_name: 'all' });
      }
    });
  }
};
// 标签全选功能
// 递归获取 所有列最后一层 的tag_id
const extractLastLayerTagIds = (items: any[], tagIds: number[] = []): number[] => {
  items.forEach(item => {
    if (item.children && item.children.length > 0) {
      extractLastLayerTagIds(item.children, tagIds);
    } else if (item.tag_id) {
      tagIds.push(item.tag_id);
    }
  });
  return tagIds;
};
const tagsAllSelected = ref<boolean>(false);
// 全选/取消全选
const tagsHandleSelectAll = () => {
  const allTagIds: number[] = extractLastLayerTagIds(tagOpts.value);
  if (searchForm.value.tags?.length < allTagIds.length) {
    searchForm.value.tags = [];
    searchForm.value.tags = allTagIds;
  } else {
    searchForm.value.tags = [];
  }
};
// 标签选择时 判断是否全选
const handleTagChange = (val: number[]) => {
  const allTagIds: number[] = extractLastLayerTagIds(tagOpts.value);
  if (val.length === allTagIds.length) {
    tagsAllSelected.value = true;
  } else {
    tagsAllSelected.value = false;
  }
};
const handleTagType = () => {
  searchForm.value.tags = [];
};
// 查询聊天列表模块
const chatListData = reactive({
  list: [] as Record<string, unknown>[], // 表格数据
  total: 0, // 总数
  page: 1, // 当前页
  pageSize: 20, // 每页条数
});

const poolLoading = ref(false);
const search = async () => {
  poolLoading.value = true;
  try {
    // 构建查询参数
    const params = {
      email_id: searchForm.value.email_id,
      page: chatListData.page,
      page_size: chatListData.pageSize,
      keyword: searchForm.value.keyword || undefined,
      sender_email: searchForm.value.sender_email || undefined,
      receiver_email: searchForm.value.receiver_email || undefined,
      status: searchForm.value.status || undefined,
      has_unread: searchForm.value.has_unread,
      has_attachments: searchForm.value.has_attachments,
      priority: searchForm.value.priority,
      start_time: searchForm.value.start_time || undefined,
      end_time: searchForm.value.end_time || undefined,
      last_reply_service: searchForm.value.last_reply_service || undefined,
    };

    // 移除undefined值
    Object.keys(params).forEach(key => {
      if (params[key] === undefined) {
        delete params[key];
      }
    });

    const res = await getThreadsList(params);
    chatListData.list = res.data || [];
    chatListData.total = parseInt(res.total) || 0;
    chatListData.page = res.page || 1;
    chatListData.pageSize = res.page_size || 20;
  } catch (error) {
    console.error('搜索邮件失败:', error);
  } finally {
    poolLoading.value = false;
  }
};
const handleSizeChange = (val: number) => {
  chatListData.page = 1;
  chatListData.pageSize = val;
  search();
};
const handleCurrentChange = (val: number) => {
  chatListData.page = val;
  search();
};

// 点击具体邮件对象
const activechartInfo = ref<Record<string, any>>({});
const activeChatHandle = async (v: Record<string, unknown>) => {
  activeEmailInfo.value = v;
  // 加载邮件详情
  await loadEmailDetail();
  // 标记列表项为选中状态
  chatListData.list.forEach((item: any) => {
    item.active = item.id === v.id;
  });
};
// 聊天列表list自动滚动active item功能
const refreshTable = () => {
  if (activechartInfo.value) {
    const element = document.querySelector(
      `#item${activechartInfo.value.dsc_user_id}${activechartInfo.value.dm_channel}`
    );
    if (element) {
      element.scrollIntoView();
    }
  }
};
const gameList = computed(() => useEnumStore().gameList);
const enumList = computed(() => useEnumStore().enumList);
const langList = computed(() => useEnumStore().LangsList);
const searchFormRef = ref<FormInstance>();
const searchForm = ref<SearchForm>({ ...searchFormInitData });
// DC机器人列表（根据游戏变化）
const botsList = ref<Record<string, string>>({});
watch(
  () => searchForm.value.project,
  n => {
    searchForm.value.bot_ids = [];
    if (n.length === 0) {
      botsList.value = {};
      return;
    }
    robotList({ projects: n }).then((res: any) => {
      botsList.value = res;
    });
  },
  { immediate: true }
);
const baseForm = ref<BaseForm>({} as BaseForm);
const activeShortcutId = ref(0);
const activeTabId = ref<number>(0);
const shortcutHandle = (v: shortcutButtonT) => {
  // console.log('v', v)
  activeTabId.value = 0;
  activeShortcutId.value = v.id;
  searchForm.value = { ...searchFormInitData };
  Object.keys(v.shortcut).forEach(key => {
    if (key in searchForm.value) {
      // console.log('key', key)
      if (key === 'last_reply_service' && v.shortcut[key] === 'CurrentUser') {
        (searchForm.value[key as SearchFormKey] as SearchFormValue) = Array.of(
          userInfo.value.username
        ) as SearchFormValue;
      } else {
        (searchForm.value[key as SearchFormKey] as SearchFormValue) = v.shortcut[
          key
        ] as SearchFormValue;
      }
    }
  });
  search();
};
// 自动加载第一个按钮查询数据
shortcutHandle(ovButtonList[0]);
// 重置按钮
const reset = () => {
  searchFormRef.value?.resetFields();
  shortcutHandle(ovButtonList[0]);
  poolSortType.value = null;
  orderBy.value = '';
};
// 获取概览数据
const getOverview = async () => {
  try {
    const res = await getStats({});
    Object.keys(overviewData).forEach(key => {
      overviewData[key] = res[key];
    });
  } catch (error) {
    console.log(error);
  }
};
getOverview();
// 下载
const downloadData = () => {
  const { dsc_user_nickname, pay_prev, pay_next, pay_all_prev, pay_all_next } = searchForm.value;
  const params = Object.assign({}, searchForm.value, {
    dsc_user_nickname: [dsc_user_nickname].filter(Boolean),
    // uid: Number(uid) === 0 ? null : Number(uid),
    pay_last_thirty_days: [pay_prev, pay_next].filter(Boolean).filter(item => item !== undefined),
    pay_all: [pay_all_prev, pay_all_next].filter(Boolean).filter(item => item !== undefined),
    page: 1,
    page_size: 20,
  });
  discordDown(params);
};
// 获取玩家画像
const getPlayerInfo = () => {
  portraitLoading.value = true;
  tagLists.value = [];
  getPortrait({
    dsc_user_id: activechartInfo.value.dsc_user_id,
    game_project: activechartInfo.value.project,
  })
    .then((res: any) => {
      if (res.label) {
        tagLists.value = res.new_label;
      }
      baseForm.value = { ...res };
    })
    .catch((error: Error) => {
      console.error('Failed to get player info:', error);
    })
    .finally(() => {
      portraitLoading.value = false;
    });
};
const handleTagToggle = () => {
  tagEditVisible.value = true;
  paramsData.value = {
    dsc_user_id: activechartInfo.value.dsc_user_id,
    game_project: activechartInfo.value.project,
  };
  tagData.value = {
    ...baseForm.value,
    game_project: activechartInfo.value.project,
  };
};
const handleBaseToggle = () => {
  baseBtnText.value = isEditMode.value ? $t('btn_edit') : $t('text_save');
  isEditMode.value = !isEditMode.value;
  if (!isEditMode.value) {
    handleSaveAndRefresh();
  }
};
const handleRemarkToggle = () => {
  remarkBtnText.value = isRemarkEditMode.value ? $t('btn_edit') : $t('text_save');
  isRemarkEditMode.value = !isRemarkEditMode.value;
  if (!isRemarkEditMode.value) {
    handleSaveAndRefresh();
  }
};
// 画像保存
const handleSaveAndRefresh = () => {
  const params = Object.assign({}, baseForm.value, {
    dsc_user_id: activechartInfo.value.dsc_user_id,
    game_project: activechartInfo.value.project,
    id: baseForm.value.id,
    label: (tagLists.value || []).map((item: any) => item.tag_id).join(','),
  });
  addPortrait(params).then(() => {
    ElMessage.success($t('text_success'));
    getPlayerInfo();
  });
};
const searchHandle = () => {
  getPlayerInfo();
};
// 30累计付费金额左侧范围判断
const getPayVal = () => {
  searchForm.value.pay_prev = searchForm.value.pay_prev || 0;
  if (searchForm.value.pay_next === undefined) {
    searchForm.value.pay_next = 0;
  }
  if (searchForm.value.pay_prev === 0 && searchForm.value.pay_next === 0) {
    searchForm.value.pay_prev = undefined;
    searchForm.value.pay_next = undefined;
  }
};
// 30累计付费金额右侧范围判断
const getPayNextVal = () => {
  searchForm.value.pay_next = searchForm.value.pay_next || 0;
  if (searchForm.value.pay_next && !searchForm.value.pay_prev) {
    searchForm.value.pay_prev = 0;
  }
  if (searchForm.value.pay_prev === 0 && searchForm.value.pay_next === 0) {
    searchForm.value.pay_prev = undefined;
    searchForm.value.pay_next = undefined;
  }
};
// 累计付费金额左侧范围判断
const getPayAllVal = () => {
  searchForm.value.pay_all_prev = searchForm.value.pay_all_prev || 0;
  if (searchForm.value.pay_all_next === undefined) {
    searchForm.value.pay_all_next = 0;
  }
  if (searchForm.value.pay_all_prev === 0 && searchForm.value.pay_all_next === 0) {
    searchForm.value.pay_all_prev = undefined;
    searchForm.value.pay_all_next = undefined;
  }
}; // 累计付费金额右侧范围判断
const getPayAllNextVal = () => {
  searchForm.value.pay_all_next = searchForm.value.pay_all_next || 0;
  if (searchForm.value.pay_all_next && !searchForm.value.pay_all_prev) {
    searchForm.value.pay_all_prev = 0;
  }
  if (searchForm.value.pay_all_prev === 0 && searchForm.value.pay_all_next === 0) {
    searchForm.value.pay_all_prev = undefined;
    searchForm.value.pay_all_next = undefined;
  }
};

// 维护人全选
const checkProcessor = ref<boolean>(false);
const indeterminateProcessor = ref<boolean>(false);
watch(
  () => searchForm.value.processor,
  val => {
    if (val?.length === 0) {
      checkProcessor.value = false;
      indeterminateProcessor.value = false;
    } else if (val?.length === csList.value.length) {
      checkProcessor.value = true;
      indeterminateProcessor.value = false;
    } else {
      indeterminateProcessor.value = true;
    }
  }
);
const processorCheckAll = (val: CheckboxValueType) => {
  indeterminateProcessor.value = false;
  if (val) {
    searchForm.value.processor = csList.value.map(_ => _.account);
  } else {
    searchForm.value.processor = [];
  }
};

// 最近处理人全选
const checkLast = ref<boolean>(false);
const indeterminateLast = ref<boolean>(false);
watch(
  () => searchForm.value.last_reply_service,
  val => {
    if (val?.length === 0) {
      checkLast.value = false;
      indeterminateLast.value = false;
    } else if (val?.length === csList.value.length) {
      checkLast.value = true;
      indeterminateLast.value = false;
    } else {
      indeterminateLast.value = true;
    }
  }
);
const lastCheckAll = (val: CheckboxValueType) => {
  indeterminateLast.value = false;
  if (val) {
    searchForm.value.last_reply_service = csList.value.map(_ => _.account);
  } else {
    searchForm.value.last_reply_service = [];
  }
};

// DC机器人全选
const checkBotids = ref<boolean>(false);
const indeterminateBotids = ref<boolean>(false);
watch(
  () => searchForm.value.bot_ids,
  val => {
    if (val?.length === 0) {
      checkBotids.value = false;
      indeterminateBotids.value = false;
    } else if (val?.length === Object.keys(botsList.value).length) {
      checkBotids.value = true;
      indeterminateBotids.value = false;
    } else {
      indeterminateBotids.value = true;
    }
  }
);
const botidsCheckAll = (val: CheckboxValueType) => {
  indeterminateBotids.value = false;
  if (val) {
    searchForm.value.bot_ids = Object.keys(botsList.value);
  } else {
    searchForm.value.bot_ids = [];
  }
};

// 语言全选
const checkLang = ref<boolean>(false);
const indeterminateLang = ref<boolean>(false);
watch(
  () => searchForm.value.language,
  val => {
    if (val?.length === 0) {
      checkLang.value = false;
      indeterminateLang.value = false;
    } else if (val?.length === langList.value.length) {
      checkLang.value = true;
      indeterminateLang.value = false;
    } else {
      indeterminateLang.value = true;
    }
  }
);
const langCheckAll = (val: CheckboxValueType) => {
  indeterminateLang.value = false;
  if (val) {
    searchForm.value.language = langList.value.map((_: any) => _.code);
  } else {
    searchForm.value.language = [];
  }
};

// 邮箱全选相关变量
const checkEmails = ref<boolean>(false);
const indeterminateEmails = ref<boolean>(false);

// 邮箱全选监听
watch(
  () => searchForm.value.emails,
  val => {
    if (val?.length === 0) {
      checkEmails.value = false;
      indeterminateEmails.value = false;
    } else if (val?.length === emailList.value.length) {
      checkEmails.value = true;
      indeterminateEmails.value = false;
    } else {
      indeterminateEmails.value = true;
    }
  }
);
const emailsCheckAll = (val: CheckboxValueType) => {
  indeterminateEmails.value = false;
  if (val) {
    searchForm.value.emails = emailList.value.map(_ => _.value);
  } else {
    searchForm.value.emails = [];
  }
};

// 邮件标签全选相关变量
const checkEmailTags = ref<boolean>(false);
const indeterminateEmailTags = ref<boolean>(false);

// 邮件标签全选监听
watch(
  () => searchForm.value.email_tags,
  val => {
    if (val?.length === 0) {
      checkEmailTags.value = false;
      indeterminateEmailTags.value = false;
    } else if (val?.length === emailTagsList.value.length) {
      checkEmailTags.value = true;
      indeterminateEmailTags.value = false;
    } else {
      indeterminateEmailTags.value = true;
    }
  }
);
const emailTagsCheckAll = (val: CheckboxValueType) => {
  indeterminateEmailTags.value = false;
  if (val) {
    searchForm.value.email_tags = emailTagsList.value.map(_ => _.value);
  } else {
    searchForm.value.email_tags = [];
  }
};

// 邮件处理人全选相关变量
const checkEmailProcessor = ref<boolean>(false);
const indeterminateEmailProcessor = ref<boolean>(false);

// 邮件处理人全选监听
watch(
  () => searchForm.value.email_processor,
  val => {
    if (val?.length === 0) {
      checkEmailProcessor.value = false;
      indeterminateEmailProcessor.value = false;
    } else if (val?.length === csList.value.length) {
      checkEmailProcessor.value = true;
      indeterminateEmailProcessor.value = false;
    } else {
      indeterminateEmailProcessor.value = true;
    }
  }
);
const emailProcessorCheckAll = (val: CheckboxValueType) => {
  indeterminateEmailProcessor.value = false;
  if (val) {
    searchForm.value.email_processor = csList.value.map(_ => _.account);
  } else {
    searchForm.value.email_processor = [];
  }
};

const uniqueArr = ref<number[]>([]);
// 全选
const onAllSelectChange = () => {
  const clearSelection = () => {
    checkUserIdList.value = [];
    checkDataList.value = [];
    checkProject.value = [];
    checkBotId.value = [];
    checkEmailList.value = [];
  };

  if (checkAll.value) {
    clearSelection();
    chatListData.list.forEach((item: any) => {
      item.checked = true;
      // 兼容Discord和邮件数据结构
      if (item.dsc_user_id) {
        checkUserIdList.value.push(item.dsc_user_id);
      }
      if (item.uid !== undefined) {
        checkDataList.value.push(item.uid);
      }
      if (item.project) {
        checkProject.value.push(item.project);
      }
      if (item.bot_id) {
        checkBotId.value.push(item.bot_id);
      }
      // 邮件ID收集
      if (item.id || item.thread_id) {
        checkEmailList.value.push(item.id || item.thread_id);
      }
    });
    uniqueArr.value = [...new Set(checkDataList.value)];
  } else {
    chatListData.list.forEach((item: any) => {
      item.checked = false;
    });
    clearSelection();
  }
};
// 单选
const handleCheckItemChange = (val: {
  checked: boolean;
  dsc_user_id?: string;
  uid?: number;
  bot_id?: string;
  project?: string;
  id?: number | string;
  thread_id?: string;
}) => {
  const updateList = (list: any[], value: any, action: 'add' | 'remove') => {
    const index = list.indexOf(value);
    if (action === 'add') {
      list.push(value);
    } else if (action === 'remove' && index > -1) {
      list.splice(index, 1);
    }
  };
  if (val.checked) {
    // 兼容Discord和邮件数据结构
    if (val.uid !== undefined) {
      updateList(checkDataList.value, val.uid, 'add');
    }
    if (val.dsc_user_id) {
      updateList(checkUserIdList.value, val.dsc_user_id, 'add');
    }
    if (val.project) {
      updateList(checkProject.value, val.project, 'add');
    }
    if (val.bot_id) {
      updateList(checkBotId.value, val.bot_id, 'add');
    }
    // 邮件ID收集
    if (val.id || val.thread_id) {
      updateList(checkEmailList.value, val.id || val.thread_id, 'add');
    }
    uniqueArr.value = [...new Set(checkDataList.value)];
  } else {
    if (val.uid !== undefined) {
      updateList(checkDataList.value, val.uid, 'remove');
    }
    if (val.dsc_user_id) {
      updateList(checkUserIdList.value, val.dsc_user_id, 'remove');
    }
    if (val.project) {
      updateList(checkProject.value, val.project, 'remove');
    }
    if (val.bot_id) {
      updateList(checkBotId.value, val.bot_id, 'remove');
    }
    // 邮件ID移除
    if (val.id || val.thread_id) {
      updateList(checkEmailList.value, val.id || val.thread_id, 'remove');
    }
    uniqueArr.value = [...new Set(checkDataList.value)];
  }
};
// 批量私信
const showBatchBox = ref(false);
const handleAllCheckData = () => {
  const projectList = [...new Set(checkProject.value)];
  if (projectList.length > 1) {
    ElMessage.error($t('text_path_message_single_game'));
    return;
  }
  showBatchBox.value = true;
  messageData.value = {
    bot_id: [...new Set(checkBotId.value)].join(','),
    project: projectList[0],
  };
};
// 中间内容排序
const handleSortAsc = () => {
  orderBy.value = 'asc';
};
const handleSortDesc = () => {
  orderBy.value = 'desc';
};
// 点击添加备注
const handleClickMark = (index: number) => {
  isTimerActive.value = false;
  chatListData.list[index].showMarkInput = true;
};
// 失去焦点关闭文本框，并发送请求
const handleInputBlur = (index: number) => {
  const item = chatListData.list[index];
  if (!item) {
    // 如果索引无效，则不执行任何操作
    return;
  }
  // if(!item.note) {
  //   item.showMarkInput = false
  //   return
  // }
  item.showMarkInput = false;
  addMark({
    channel_id: item.dm_channel,
    note: item.note,
  })
    .then(() => {
      ElMessage.success($t('text_add_mark_success'));
      isTimerActive.value = true;
    })
    .catch((error: any) => {
      // 处理可能的错误
      console.log('Failed to add note:', error);
      isTimerActive.value = true;
    });
};
// 修正客服对玩家消息的回复状态
const handleRefresh = (val: { dsc_user_id: string; dm_channel: string; status: number }) => {
  discordReplyStatus({
    channel_id: val.dm_channel,
    dsc_user_id: val.dsc_user_id,
    old_reply_status: val.status,
    new_reply_status: val.status === 1 ? 2 : 1,
  }).then(() => {
    ElMessage.success($t('text_refresh_success'));
    search();
  });
};
// 固定tab
const handleAddTabData = () => {
  const { project, dsc_user_nickname, pay_prev, pay_next, pay_all_prev, pay_all_next } =
    searchForm.value;
  if (project.length > 1) {
    ElMessage.error($t('text_only_supports_creating_single_game'));
    return;
  }
  if (pay_prev !== undefined && pay_next !== undefined && pay_prev > pay_next) {
    ElMessage.error($t('text_thirty_pay_left_cant_over_right'));
    return;
  }
  if (pay_all_prev !== undefined && pay_all_next !== undefined && pay_all_prev > pay_all_next) {
    ElMessage.error($t('text_total_pay_left_cant_over_right'));
    return;
  }
  tabVisible.value = true;
  const params = Object.assign({}, searchForm.value, {
    dsc_user_nickname: [dsc_user_nickname].filter(Boolean),
    // uid: Number(uid) === 0 ? null : Number(uid),
    pay_last_thirty_days: [pay_prev, pay_next].filter(item => item !== undefined),
    pay_all: [pay_all_prev, pay_all_next].filter(item => item !== undefined),
    page: chatListData.page,
    page_size: chatListData.pageSize,
  });
  tabData.value = params;
};
// 固定tab-点击事件
const handleClickTab = (v: { id: number; detail: SearchForm }) => {
  activeShortcutId.value = 0;
  activeTabId.value = v.id;
  if (v.detail) {
    const { dsc_user_nickname, pay_last_thirty_days, pay_all } = v.detail;
    const params = { ...v.detail };
    // 检查 pay_last_thirty_days 和 pay_all 是否已定义且不是 undefined，且长度大于0
    if (Array.isArray(pay_last_thirty_days) && pay_last_thirty_days.length > 0) {
      params.pay_prev = pay_last_thirty_days[0];
      if (pay_last_thirty_days.length > 1) {
        params.pay_next = pay_last_thirty_days[1];
      }
    }
    if (Array.isArray(pay_all) && pay_all.length > 0) {
      params.pay_all_prev = pay_all[0];
      if (pay_all.length > 1) {
        params.pay_all_next = pay_all[1];
      }
    }
    // 检查 dsc_user_nickname 是否是数组且不为空
    if (Array.isArray(dsc_user_nickname) && dsc_user_nickname.length > 0) {
      params.dsc_user_nickname = dsc_user_nickname[0];
    } else {
      params.dsc_user_nickname = '';
    }
    if (Number(params.tag_type) === 0) {
      params.tag_type = null;
    }
    // 其他属性赋值
    params.page = chatListData.page;
    params.page_size = chatListData.pageSize;
    searchForm.value = params;
  }
  search();
};
// 编辑tab
const handleEditTab = (v: { id: number; tab_name: string; public: number }) => {
  tabVisible.value = true;
  tabData.value = {
    ...searchForm.value,
    tab_name: v.tab_name,
    id: v.id,
    public: v.public,
  };
};
// 删除tab
const handleDelete = async (v: { id: number; tab_name: string }) => {
  ElMessageBox.confirm(`确定要删除名称为: ( ${v.tab_name} )的Tab吗?`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    tabsLoading.value = true;
    deleteTab({ id: v.id })
      .then(() => {
        ElMessage.success($t('text_del_success'));
        getTabList();
        tabsLoading.value = false;
        reset();
      })
      .catch((error: any) => {
        console.log(error);
        tabsLoading.value = false;
      });
  });
};
// 批量打标签
const handlePatchTag = () => {
  const projectList = [...new Set(checkProject.value)];
  if (projectList.length > 1) {
    ElMessage.error($t('text_patch_tags_only_single_game'));
    return;
  }
  patchTagVisible.value = true;
  projectData.value = {
    project: projectList.join(','),
  };
};

// 批量删除标签
const batchDelTagVisible = ref(false);
const batchDelTagHandle = () => {
  const projectList = [...new Set(checkProject.value)];
  if (projectList.length > 1) {
    ElMessage.error($t('text_batch_del_tags_only_single_game'));
    return;
  }
  batchDelTagVisible.value = true;
  projectData.value = {
    project: projectList.join(','),
  };
};

// 判断对象value是否有值
const isObjectNonEmpty = (obj: any): boolean => {
  // 检查对象是否为空或未定义
  if (!obj || typeof obj !== 'object') {
    return false;
  }
  // 使用for...in循环遍历对象的所有可枚举属性
  for (const key in obj) {
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      const value = obj[key];
      // 检查值是否不是undefined、null、空字符串""或空数组[]
      if (
        value !== undefined &&
        value !== null &&
        !(typeof value === 'string' && value === '') &&
        !(Array.isArray(value) && value.length === 0)
      ) {
        // 如果找到至少一个非空值，则返回true
        return true;
      }
    }
  }
  // 如果没有找到任何非空值，则返回false
  return false;
};
const activeTab = ref(1);
const tabLoading = ref(false);
const changeTabHandle = () => {
  // tabLoading.value = true
  // setTimeout(() => {
  //   tabLoading.value = false
  // }, 1000)
};
provide('activeTab', activeTab);

// 耦合信息跳转路由
const jumpHandle = (v: string) => {
  const newTab = router.resolve({
    name: 'Overview',
    query: { ticket_id: v, origin: 'coupler' },
  });
  window.open(newTab.href, '_blank');
};

// 历史工单点击新开页面跳转当前工单
const historyTicketHandle = (v: any) => {
  jumpHandle(v.ticket_id);
};

const timer: any = null;
const tabTimer: any = null;
onMounted(async () => {
  // 检查query是否有fpid，如果有,说明是工单跳转过来的，需要自动查询并自动选中，展示详情
  const query = useRoute().query;
  if (query.fpid) {
    searchForm.value.fpid = query.fpid as string;
    await search();
    if (chatListData.list.length > 0) {
      activeChatHandle(chatListData.list[0]);
    } else {
      ElMessage.error('数据错误，并未找到对应的聊天');
    }
  }

  // todo 暂时关闭
  // if(isTimerActive.value) {
  //   // 每1分钟刷新一次数据
  //   timer = setInterval(() => {
  //     if(isTimerActive.value) {// 确保在每次执行前检查状态
  //       search()
  //       getOverview()
  //     }
  //   }, 30000)
  // }
  // // 自定义数据每个5分钟刷新一次
  // tabTimer = setInterval(() => {
  //   getTabList()
  // }, 300000)
  document.body.addEventListener('click', () => {
    chatListData.list.map(item => {
      item.showMarkInput = false;
    });
  });

  // 初始化排序
  nextTick(() => {
    initSortable();
  });

  // 监听tabList变化重新初始化拖拽
  watch(
    tabList,
    () => {
      nextTick(() => {
        initSortable();
      });
    },
    { deep: true }
  );
});
onUnmounted(() => {
  clearInterval(timer);
  clearInterval(tabTimer);
});

const updateSortable = () => {
  const menuEl = document.querySelectorAll('.project-sortable-tree .el-sub-menu');
  if (!menuEl) return;

  const sort = [];
  menuEl.forEach(el => {
    const project = (el.querySelector('.el-sub-menu__title span') as HTMLElement)?.innerText;
    const tabs = el.querySelectorAll('.el-menu-item');

    const sortTabs = [];
    tabs.forEach(tab => {
      const tabId = tab.getAttribute('data-id');
      if (tabId) {
        sortTabs.push(Number(tabId));
      }
    });

    sort.push({
      project: project,
      tab: sortTabs,
    });
  });

  updateTabSettingOrder({
    sort_setting: JSON.stringify(sort),
  })
    .then(() => {
      ElMessage.success('排序成功');
    })
    .catch(() => {
      ElMessage.error('排序失败');
    });
};

const initSortable = () => {
  const menuEl = document.querySelector('.project-sortable-tree');
  if (!menuEl) return;

  new Sortable(menuEl, {
    animation: 150,
    handle: '.el-sub-menu__title',
    onEnd: () => {
      updateSortable();
    },
  });

  const menuSubEl = document.querySelectorAll('.project-sortable-tree .el-menu--inline');
  menuSubEl.forEach(el => {
    new Sortable(el, {
      animation: 150,
      handle: '.el-button',
      onEnd: () => {
        updateSortable();
      },
    });
  });
};
</script>

<style lang="scss" scoped>
.operating-space {
  height: 100%;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  padding: 5px 15px;

  .base-info {
    width: 100%;
    margin-bottom: 10px;
    &:deep(.el-descriptions__title) {
      width: 100% !important;
    }
    &:deep(.el-descriptions__header) {
      margin-bottom: 0px;
    }
  }

  .tab-info {
    width: 100%;
    flex-grow: 1;
    overflow: auto;
    .el-tabs {
      height: 100%;
      display: flex;
      flex-direction: column;
      &:deep(.el-tabs__content) {
        flex: 1;
        overflow: hidden;
      }
      .el-tab-pane {
        height: 100%;
        overflow: auto;
      }
    }
  }

  .process-area {
    width: 100%;
    min-height: 200px;
    border-top: 1px solid #e4e7ed;
    padding-top: 10px;
    margin-top: 10px;

    .remark-btn {
      margin-right: 8px;
      margin-bottom: 8px;
    }

    .edit-box {
      margin-bottom: 10px;
    }

    .btn-box {
      display: flex;
      gap: 8px;
      flex-wrap: wrap;
    }
  }
  .chat-tab-box {
    width: 100%;
    flex-grow: 1;
    overflow: auto;
    .el-tabs {
      height: 100%;
      display: flex;
      flex-direction: column;
      &:deep(.el-tabs__content) {
        flex: 1;
      }
      .el-tab-pane {
        height: 100%;
        overflow: auto;
        .el-descriptions {
          cursor: pointer;
          width: 100%;
          margin: 0px auto 30px;
          padding: 8px;
          border-radius: 5px;
          background-color: #f8f9fa;
        }
      }
    }
    .common-title-box {
      display: flex;
      justify-content: space-between;
      height: 24px;
      line-height: 24px;
      margin-top: 10px;
      .common-title {
        font-size: 14px;
        font-weight: bold;
      }
    }
    .tag-box {
      max-height: 100px;
      overflow: auto;
      position: relative;
      margin-top: 10px;
      .no-data-box {
        height: 30px;
      }
      .no-data {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-size: 14px;
        color: #909399;
      }
    }
    .base-info {
      margin-top: 10px;
      padding-left: 10px;
    }
    .remark-box {
      margin-top: 10px;
    }
  }
}
.split-box {
  height: 100%;
  .pane-wapper {
    box-sizing: border-box;
    padding: 10px 5px;
    .hor-line {
      margin: 0 2px;
      font-size: 16px;
      font-weight: bold;
      color: #606266;
    }
    &:deep(.el-input-number) {
      .el-input {
        min-width: 40px;
      }
    }
    &:deep(.el-input-number--small) {
      // width: 50px;
      .el-input-number__increase {
        display: none !important;
      }
      .el-input-number__decrease {
        display: none !important;
      }
    }
  }

  .o-left {
    .el-scrollbar {
      height: calc(100% - 62px);
    }
    &:deep(.el-sub-menu__title) {
      height: 40px;
      line-height: 40px;
      &:hover {
        background-color: transparent;
      }
    }
    &:deep(.el-menu-item) {
      padding: 0;
      &:hover {
        background-color: transparent;
      }
    }
    .delete-btn {
      padding: 5px;
      position: absolute;
      right: -10px;
      visibility: hidden;
    }
    .edit-btn {
      position: absolute;
      right: 20px;
      visibility: hidden;
    }
    &:deep(.el-button) {
      &:hover {
        .delete-btn,
        .edit-btn {
          visibility: visible !important;
          color: #4aa181;
          background-color: #edf6f2;
          transition: background-color 0.2s;
        }
      }
    }
    .activeBtn {
      color: var(--el-button-hover-text-color);
      border-color: var(--el-button-hover-border-color);
      background-color: var(--el-button-hover-bg-color);
      outline: none;
    }

    .el-button {
      display: block;
      margin: 2px auto 20px;
      width: 90%;
      overflow: hidden;
    }

    .title {
      font-size: 18px;
      height: 40px;
      line-height: 40px;
      font-weight: bold;
      text-align: center;
      -webkit-background-clip: text;
      background-clip: text;
      color: transparent;
      background-image: linear-gradient(to right, rgba(39, 177, 236, 0.8), rgba(74, 161, 129, 0.8));
      box-shadow: 0px 2px 7px -3px rgba(0, 0, 0, 0.6);
      margin-bottom: 20px;

      .text {
        margin-left: 10px;
      }
    }
  }
}
.abbreviation-wapper {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  .dis-checkbox {
    float: left;
    margin-right: 5px;
  }
  .list-total-tip {
    float: left;
    margin-top: 5px;
    margin-bottom: 10px;
    font-size: 14px;
    font-weight: bold;
    color: var(--el-text-color-regular);
  }
  &:deep(.my-label) {
    width: 120px;
    // text-align: right;
  }
  &:deep(.right-label) {
    // width: 120px;
    text-align: right;
  }
  &:deep(.el-form--inline .el-form-item) {
    margin-bottom: 10px;
    margin-right: 0px;
  }
  &:deep(.el-card__header, .el-card__footer) {
    padding: 10px 10px 0px;
  }

  &:deep(.el-card__footer) {
    border: 0px;
    padding: 10px;
  }

  &:deep(.el-card__body) {
    flex-grow: 1;
    padding: 10px 0px;
    overflow: hidden;
    .el-descriptions {
      cursor: pointer;
      width: 96%;
      margin: 0px auto;
      padding: 8px;
      border-radius: 5px 5px 0px 0px;
      background-color: #f8f9fa;
    }
    .coupled-data {
      background: var(--el-color-danger-light-9);
      padding: 4px 9px 2px;
      margin: 0px auto 0px;
      width: 96%;
      border-radius: 0px 0px 5px 5px;
      color: var(--el-text-color-regular);
      font-size: 12px;
      line-height: 1.5em;
      .link-btn {
        color: var(--el-color-primary);
        cursor: pointer;
        &:hover {
          text-decoration: underline;
        }
        line-height: 1.5em;
      }
    }
    .item-waper {
      margin-bottom: 20px;
      border-radius: 5px;
      overflow: hidden;
    }
  }
  .dis-flex {
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;

    .chat-list {
      width: 100%;
      flex-grow: 1;
      .mark-color {
        color: green;
        white-space: normal;
        text-align: left;
        line-height: 1.5rem;
      }
    }
  }
  .pagination {
    float: right;
  }
  .sort-box {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 5px;
    &:deep(.el-icon) {
      margin-top: -5px;
      font-size: 16px;
    }
  }
}
.dc-list-title {
  margin-left: 2px;
  span {
    display: inline-block;
    height: 32px;
    line-height: 32px;
  }
  .mr-36 {
    margin-right: 36px;
  }
}
.base-info-form {
  .el-form-item {
    margin: 8px 10px 15px;
  }
}

/* 邮件卡片样式 */
.email-item-wrapper {
  margin-bottom: 8px;

  .email-card {
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    padding: 10px 12px;
    background-color: #fff;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      border-color: #4aa181;
      box-shadow: 0 2px 8px rgba(74, 161, 129, 0.15);
    }

    &.active {
      border-color: #4aa181;
      background-color: #f0f9ff;
      box-shadow: 0 2px 8px rgba(74, 161, 129, 0.2);
    }

    .email-header {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
      border-bottom: 1px solid #f0f0f0;

      .email-checkbox {
        margin-right: 8px;
      }

      .email-meta {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 12px;
        color: #909399;
        gap: 10px;

        .email-id {
          font-weight: 500;
          color: #606266;
        }

        .email-box {
          color: #909399;
        }

        .email-actions {
          margin-left: auto;
        }
      }
    }

    .email-content {
      margin-left: 24px; // 对齐checkbox的缩进

      .sender-info {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 4px;
        font-size: 13px;

        .sender-name {
          font-weight: 700;
          color: #333;
        }

        .sender-email {
          color: #909399;
        }

        .send-date {
          margin-left: auto;
          color: #909399;
          font-size: 12px;
        }
      }

      .email-title {
        font-size: 14px;
        font-weight: 700;
        color: #333;
        margin-bottom: 4px;
        line-height: 1.3;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .email-body {
        font-size: 12px;
        color: #666;
        height: 18px;
        line-height: 18px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        margin-bottom: 2px;
        ::v-deep(p) {
          margin: 0;
        }
      }
    }

    .email-footer {
      margin-left: 24px; // 对齐checkbox的缩进
      margin-top: 6px;
      padding-top: 6px;
      border-top: 1px solid #f0f0f0;

      .email-stats {
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 12px;
        color: #909399;

        .message-count {
          color: #606266;
        }

        .unread-count {
          color: #f56c6c;
          font-weight: 500;
        }

        .has-attachment {
          color: #909399;
        }

        .email-status {
          margin-left: auto;
        }
      }
    }
  }
}

.base-info {
  margin-bottom: 15px !important;
  &::v-deep(.el-descriptions__header) {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: 10px 0;
  }
  &::v-deep(.el-descriptions__title) {
    width: auto !important;
  }
  &::v-deep(.el-descriptions__extra) {
    display: flex;
    flex-shrink: 0;
  }
}

.basic-info-title {
  font-size: 14px;
  font-weight: bold;
  color: #303133;
}

.tab-info {
  margin-bottom: 15px;
  .tab-info-title {
    font-size: 14px;
    font-weight: bold;
    color: #303133;
    margin-bottom: 10px;
  }
}

.message-info {
  display: flex;
  flex-direction: column;
  .message-top {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    margin-bottom: 5px;
  }
  .message-to {
    margin-bottom: 5px;
  }
}
</style>
<style lang="scss">
// 标签全选 隐藏第一个自带的checkbox
.tagsCascaderClass {
  .el-cascader-menu:first-child {
    li.el-cascader-node:first-child {
      span.el-checkbox:first-child {
        display: none;
      }
    }
  }
}
</style>
