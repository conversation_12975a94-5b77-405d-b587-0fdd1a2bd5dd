// 邮件相关类型定义

export interface SearchForm {
  // 邮件相关字段
  email_id?: number;
  page?: number;
  page_size?: number;
  keyword?: string;
  sender_email?: string;
  receiver_email?: string;
  status?: string | string[];
  has_unread?: boolean;
  has_attachments?: boolean;
  priority?: number;
  start_time?: string;
  end_time?: string;
  created_start_time?: string;
  created_end_time?: string;
  thread_id?: string;
  tags?: number[];
  processor?: string[];
  remark_search?: string;
  emails?: string[];
  email_tags?: string[];
  email_processor?: string[];
  reply_time?: string[];
  created_time?: string[];

  // 保留原有字段以兼容
  project?: string[];
  replied_at?: Array<string>;
  last_login?: Array<string>;
  pay_all_prev?: number;
  pay_all_next?: number;
  pay_prev?: number;
  pay_next?: number;
  vip_state?: number;
  tag?: string[];
  dsc_user_nickname?: string;
  bot_ids?: string[];
  user_content?: string;
  user_detail_remark?: string;
  dsc_user_id?: string;
  uids?: number;
  fpid?: string;
  sid?: string;
  bot_id?: string;
  note?: string;
  checked?: boolean;
  birthday?: string;
  language?: string[];
  dm_channel?: string;
  pay_last_thirty_days?: number[] | undefined;
  pay_all?: number[] | undefined;
  reply_type?: number | undefined;
  last_reply_service?: string[];
  tag_type?: number | null;
  tab_name?: string;
  id?: number;
  public?: number;
}

export interface EmailItem {
  id: number | string;
  thread_id?: string;
  checked?: boolean;
  active?: boolean;
  subject?: string;
  original_sender_name?: string;
  original_sender_email?: string;
  system_receiver_email?: string;
  created_at?: string;
  last_preview?: string;
  status?: string;
  message_count?: number;
  unread_count?: number;
  has_attachments?: boolean;
  processor?: string;
  tags?: string[];
  remark?: string;
  messages?: EmailMessage[];
  email_id?: string;
  last_reply_service?: string;
}

export interface EmailMessage {
  message: {
    from: string;
    to: string;
    subject?: string;
    status: 'sent' | 'received';
    sent_at?: string;
    received_at?: string;
  };
  content: {
    body: string;
    attachments?: EmailAttachment[];
  };
}

export interface EmailAttachment {
  id: string;
  file_name: string;
  size: number;
  file_url?: string;
}

export interface ChatListData {
  list: EmailItem[];
  total: number;
  page: number;
  pageSize: number;
}

export interface ReplyForm {
  reply_type: string;
  reply_body: string;
}

export interface ShortcutButton {
  id: number;
  label: string;
  shortcut: Record<string, unknown>;
  prop: string;
}

export interface TabItem {
  id: number;
  tab_name: string;
  count: number;
  operator: string;
  public: number;
  detail?: SearchForm;
}

export interface ProjectTab {
  project: string;
  tabs: TabItem[];
}

export interface EmailStatusOption {
  value: number;
  label: string;
}

export interface EmailSortOption {
  value: string;
  name: string;
}

export interface EmailOption {
  value: string;
  label: string;
}

export interface CsUser {
  account: string;
}

export interface LabelObject {
  tag_id: number;
  tag_name: string;
}

export interface BaseForm {
  tag: number[];
  id: number;
  dsc_user_id: string;
  fpid: string;
  game_project: string;
  tags?: LabelObject[];
  gender?: number;
  birthday: string;
  career: string;
  education_level?: number;
  married_state?: number;
  fertility_state?: number;
  remark: string;
}
