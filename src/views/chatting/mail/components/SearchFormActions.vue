<template>
  <div class="search-form-actions">
    <!-- 搜索操作按钮 -->
    <el-form-item>
      <el-button type="primary" plain icon="Search" @click="handleSearch">
        {{ $t('btn_search') }}
      </el-button>
    </el-form-item>

    <!-- 重置按钮 -->
    <el-form-item>
      <el-button icon="Refresh" @click="handleReset">
        {{ $t('btn_reset') }}
      </el-button>
    </el-form-item>

    <!-- 下载按钮 -->
    <el-form-item>
      <el-button
        type="primary"
        icon="Download"
        @click="handleDownload"
        v-has="'discord:download'"
        :loading="progState"
        :disabled="progState"
      >
        {{ $t('btn_download') }}
        <span v-if="progState">{{ progressNum + '%' }}</span>
      </el-button>
    </el-form-item>

    <!-- 固定tab -->
    <el-form-item>
      <el-button type="primary" icon="AddLocation" @click="handleAddTab">
        {{ $t('text_fixed_tab') }}
      </el-button>
    </el-form-item>

    <!-- 批量指派 -->
    <el-form-item>
      <el-button
        type="primary"
        icon="User"
        @click="handleBatchAssign"
        :disabled="checkEmailList.length === 0"
      >
        {{ $t('btn_batch_assign') }}
      </el-button>
    </el-form-item>
  </div>
</template>

<script setup lang="ts">
import { useI18n } from 'vue-i18n';

interface Props {
  progState: boolean;
  progressNum: number;
  checkEmailList: (number | string)[];
}

const props = defineProps<Props>();

const emit = defineEmits<{
  (event: 'search'): void;
  (event: 'reset'): void;
  (event: 'download'): void;
  (event: 'add-tab'): void;
  (event: 'batch-assign'): void;
}>();

const { t: $t } = useI18n();

const handleSearch = () => {
  emit('search');
};

const handleReset = () => {
  emit('reset');
};

const handleDownload = () => {
  emit('download');
};

const handleAddTab = () => {
  emit('add-tab');
};

const handleBatchAssign = () => {
  emit('batch-assign');
};
</script>

<style lang="scss" scoped>
.search-form-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;

  .el-form-item {
    margin-bottom: 10px;
    margin-right: 0;
  }
}
</style>
