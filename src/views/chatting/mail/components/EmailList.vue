<template>
  <el-card class="abbreviation-wapper" shadow="never" v-loading="poolLoading">
    <template #header>
      <div class="list-header">
        <span class="dis-checkbox">
          <el-checkbox
            v-model="checkAll"
            size="small"
            @change="onAllSelectChange"
          ></el-checkbox>
        </span>
        <span class="list-total-tip">
          {{ $t('text_result_total', { total: chatListData.total }) }}
        </span>
        
        <!-- 排序控件 -->
        <EmailListSort
          :order-by="orderBy"
          :pool-sort-type="poolSortType"
          :email-sort-options="emailSortOptions"
          @sort-asc="handleSortAsc"
          @sort-desc="handleSortDesc"
          @sort-change="handleSortChange"
        />
      </div>
    </template>

    <div class="dis-flex">
      <el-scrollbar class="chat-list">
        <template v-if="chatListData.list.length > 0">
          <EmailCard
            v-for="(v, k) in chatListData.list"
            :key="v.id || k"
            :email="v"
            @click="handleEmailClick"
            @check-change="handleCheckItemChange"
            @assign="handleAssignEmail"
          />
        </template>
        <el-empty v-else :description="$t('info_no_data')" />
      </el-scrollbar>
    </div>

    <template #footer>
      <el-pagination
        class="pagination"
        v-model:current-page="chatListData.page"
        :small="true"
        :page-sizes="[20, 50, 150, 200, 500, 1000]"
        v-model:page-size="chatListData.pageSize"
        :total="chatListData.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </template>
  </el-card>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useI18n } from 'vue-i18n';
import EmailCard from './EmailCard.vue';
import EmailListSort from './EmailListSort.vue';

interface EmailItem {
  id: number | string;
  thread_id?: string;
  checked?: boolean;
  active?: boolean;
  subject?: string;
  original_sender_name?: string;
  original_sender_email?: string;
  system_receiver_email?: string;
  created_at?: string;
  last_preview?: string;
  status?: string;
  message_count?: number;
  unread_count?: number;
  has_attachments?: boolean;
}

interface ChatListData {
  list: EmailItem[];
  total: number;
  page: number;
  pageSize: number;
}

interface Props {
  chatListData: ChatListData;
  poolLoading: boolean;
  checkAll: boolean;
  orderBy: string;
  poolSortType: string;
  emailSortOptions: Array<{ value: string; name: string }>;
}

const props = defineProps<Props>();

const emit = defineEmits<{
  (event: 'update:chatListData', value: ChatListData): void;
  (event: 'update:checkAll', value: boolean): void;
  (event: 'update:orderBy', value: string): void;
  (event: 'update:poolSortType', value: string): void;
  (event: 'email-click', email: EmailItem): void;
  (event: 'check-change', email: EmailItem): void;
  (event: 'all-select-change'): void;
  (event: 'assign-email', email: EmailItem): void;
  (event: 'size-change', size: number): void;
  (event: 'current-change', page: number): void;
}>();

const { t: $t } = useI18n();

const onAllSelectChange = () => {
  emit('all-select-change');
};

const handleEmailClick = (email: EmailItem) => {
  emit('email-click', email);
};

const handleCheckItemChange = (email: EmailItem) => {
  emit('check-change', email);
};

const handleAssignEmail = (email: EmailItem) => {
  emit('assign-email', email);
};

const handleSortAsc = () => {
  emit('update:orderBy', 'asc');
};

const handleSortDesc = () => {
  emit('update:orderBy', 'desc');
};

const handleSortChange = (value: string) => {
  emit('update:poolSortType', value);
};

const handleSizeChange = (size: number) => {
  emit('size-change', size);
};

const handleCurrentChange = (page: number) => {
  emit('current-change', page);
};
</script>

<style lang="scss" scoped>
.abbreviation-wapper {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;

  .list-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
  }

  .dis-checkbox {
    margin-right: 5px;
  }

  .list-total-tip {
    font-size: 14px;
    font-weight: bold;
    color: var(--el-text-color-regular);
    flex: 1;
  }

  :deep(.el-card__header, .el-card__footer) {
    padding: 10px 10px 0px;
  }

  :deep(.el-card__footer) {
    border: 0px;
    padding: 10px;
  }

  :deep(.el-card__body) {
    flex-grow: 1;
    padding: 10px 0px;
    overflow: hidden;
  }

  .dis-flex {
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;

    .chat-list {
      width: 100%;
      flex-grow: 1;
    }
  }

  .pagination {
    float: right;
  }
}
</style>
