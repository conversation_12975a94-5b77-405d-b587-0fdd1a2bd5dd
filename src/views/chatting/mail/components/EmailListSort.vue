<template>
  <div class="email-list-sort">
    <el-form size="small" :inline="true">
      <el-form-item style="margin-right: 5px">
        <div class="sort-box">
          <el-link
            icon="CaretTop"
            :style="{ color: orderBy === 'asc' ? '#4aa181' : '#606266' }"
            :underline="false"
            @click="handleSortAsc"
          ></el-link>
          <el-link
            icon="CaretBottom"
            :style="{ color: orderBy === 'desc' ? '#4aa181' : '#606266' }"
            :underline="false"
            @click="handleSortDesc"
          ></el-link>
        </div>
      </el-form-item>
      <el-form-item>
        <el-select
          :model-value="poolSortType"
          :placeholder="$t('place_select')"
          style="width: 120px"
          @update:model-value="handleSortChange"
        >
          <el-option
            v-for="(v, index) in emailSortOptions"
            :key="index"
            :label="$t(v.name)"
            :value="v.value"
          ></el-option>
        </el-select>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { useI18n } from 'vue-i18n';

interface SortOption {
  value: string;
  name: string;
}

interface Props {
  orderBy: string;
  poolSortType: string;
  emailSortOptions: SortOption[];
}

const props = defineProps<Props>();

const emit = defineEmits<{
  (event: 'sort-asc'): void;
  (event: 'sort-desc'): void;
  (event: 'sort-change', value: string): void;
}>();

const { t: $t } = useI18n();

const handleSortAsc = () => {
  emit('sort-asc');
};

const handleSortDesc = () => {
  emit('sort-desc');
};

const handleSortChange = (value: string) => {
  emit('sort-change', value);
};
</script>

<style lang="scss" scoped>
.email-list-sort {
  display: flex;
  align-items: center;

  .sort-box {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 5px;
    
    :deep(.el-icon) {
      margin-top: -5px;
      font-size: 16px;
    }
  }
}
</style>
