<template>
  <div class="process-area" v-loading="replyLoading">
    <div style="height: 100%">
      <!-- 操作按钮区域 -->
      <EmailActions
        @remark="handleRemark"
        @edit-tag="handleEditTag"
      />

      <!-- 回复编辑区域 -->
      <div class="edit-box">
        <opsEditer
          :model-value="replyForm.reply_body"
          @update:model-value="updateReplyBody"
          :uploading="isUploading"
          @update:uploading="updateIsUploading"
        />
      </div>

      <!-- 底部操作按钮 -->
      <div class="btn-box">
        <!-- 回复关闭 -->
        <el-button
          size="small"
          type="primary"
          @click="handleReply(2)"
          :disabled="!replyForm.reply_body.trim() || noPermission"
        >
          {{ $t('btn_reply_close') }}
        </el-button>
        <!-- 拒单关闭 -->
        <el-button
          size="small"
          type="primary"
          @click="handleReply(3)"
          :disabled="noPermission"
        >
          {{ $t('btn_refuse') }}
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useI18n } from 'vue-i18n';
import EmailActions from './EmailActions.vue';

interface ReplyForm {
  reply_type: string;
  reply_body: string;
}

interface EmailInfo {
  thread_id?: string;
  email_id?: string;
}

interface Props {
  activeEmailInfo: EmailInfo;
  replyForm: ReplyForm;
  replyLoading: boolean;
  noPermission: boolean;
  isUploading: boolean;
}

const props = defineProps<Props>();

const emit = defineEmits<{
  (event: 'reply', opType: number): void;
  (event: 'remark'): void;
  (event: 'edit-tag'): void;
  (event: 'update:reply-form', value: ReplyForm): void;
  (event: 'update:is-uploading', value: boolean): void;
}>();

const { t: $t } = useI18n();

const handleReply = (opType: number) => {
  emit('reply', opType);
};

const handleRemark = () => {
  emit('remark');
};

const handleEditTag = () => {
  emit('edit-tag');
};

const updateReplyBody = (value: string) => {
  const updatedForm = { ...props.replyForm, reply_body: value };
  emit('update:reply-form', updatedForm);
};

const updateIsUploading = (value: boolean) => {
  emit('update:is-uploading', value);
};
</script>

<style lang="scss" scoped>
.process-area {
  width: 100%;
  min-height: 200px;
  border-top: 1px solid #e4e7ed;
  padding-top: 10px;
  margin-top: 10px;

  .edit-box {
    margin-bottom: 10px;
    min-height: 150px;
  }

  .btn-box {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
  }
}
</style>
