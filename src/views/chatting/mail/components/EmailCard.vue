<template>
  <div class="email-item-wrapper">
    <div
      class="email-card"
      :class="{ active: email.active }"
      @click.stop="handleEmailClick"
      :id="'item' + email.id"
    >
      <div class="email-header">
        <div class="email-checkbox" @click.stop>
          <el-checkbox 
            :model-value="email.checked" 
            @change="handleCheckChange" 
          />
        </div>
        <div class="email-meta">
          <span class="email-id">邮件Id: {{ email.id || '-' }}</span>
          <span class="email-box">
            关联邮箱: {{ email.system_receiver_email || '-' }}
          </span>
          <div class="email-actions">
            <el-button
              type="primary"
              size="small"
              @click.stop="handleAssignClick"
            >
              {{ $t('text_assign') }}
            </el-button>
          </div>
        </div>
      </div>
      
      <div class="email-content">
        <div class="sender-info">
          <span class="sender-name">{{ email.original_sender_name || '-' }}</span>
          <span class="sender-email">
            &lt;{{ email.original_sender_email || '-' }}&gt;
          </span>
          <span class="send-date">{{ formatSendDate(email.created_at) }}</span>
        </div>
        <div class="email-title">{{ email.subject || '-' }}</div>
        <div class="email-body" v-html="email.last_preview || '-'"></div>
      </div>

      <!-- 可选的邮件底部信息 -->
      <div class="email-footer" v-if="showFooter">
        <div class="email-stats">
          <span class="message-count">共{{ email.message_count || 0 }}条</span>
          <span v-if="email.unread_count && email.unread_count > 0" class="unread-count">
            {{ email.unread_count }}条未读
          </span>
          <span v-if="email.has_attachments" class="has-attachment">📎</span>
          <div class="email-status">
            <el-tag :type="getStatusType(email.status)" size="small">
              {{ getStatusText(email.status) }}
            </el-tag>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useI18n } from 'vue-i18n';

interface EmailItem {
  id: number | string;
  thread_id?: string;
  checked?: boolean;
  active?: boolean;
  subject?: string;
  original_sender_name?: string;
  original_sender_email?: string;
  system_receiver_email?: string;
  created_at?: string;
  last_preview?: string;
  status?: string;
  message_count?: number;
  unread_count?: number;
  has_attachments?: boolean;
}

interface Props {
  email: EmailItem;
  showFooter?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  showFooter: false,
});

const emit = defineEmits<{
  (event: 'click', email: EmailItem): void;
  (event: 'check-change', email: EmailItem): void;
  (event: 'assign', email: EmailItem): void;
}>();

const { t: $t } = useI18n();

// 格式化发送日期
const formatSendDate = (dateStr?: string) => {
  if (!dateStr) return '-';
  const date = new Date(dateStr);
  const month = date.getMonth() + 1;
  const day = date.getDate();
  return `${month}月${day}日`;
};

// 获取状态类型
const getStatusType = (status?: string) => {
  switch (status) {
    case 'active':
      return 'success';
    case 'closed':
      return 'info';
    case 'archived':
      return 'warning';
    default:
      return 'info';
  }
};

// 获取状态文本
const getStatusText = (status?: string) => {
  switch (status) {
    case 'active':
      return '活跃';
    case 'closed':
      return '已关闭';
    case 'archived':
      return '已归档';
    default:
      return '未知';
  }
};

const handleEmailClick = () => {
  emit('click', props.email);
};

const handleCheckChange = (checked: boolean) => {
  const updatedEmail = { ...props.email, checked };
  emit('check-change', updatedEmail);
};

const handleAssignClick = () => {
  emit('assign', props.email);
};
</script>

<style lang="scss" scoped>
.email-item-wrapper {
  margin-bottom: 8px;

  .email-card {
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    padding: 10px 12px;
    background-color: #fff;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      border-color: #4aa181;
      box-shadow: 0 2px 8px rgba(74, 161, 129, 0.15);
    }

    &.active {
      border-color: #4aa181;
      background-color: #f0f9ff;
      box-shadow: 0 2px 8px rgba(74, 161, 129, 0.2);
    }

    .email-header {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
      border-bottom: 1px solid #f0f0f0;

      .email-checkbox {
        margin-right: 8px;
      }

      .email-meta {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 12px;
        color: #909399;
        gap: 10px;

        .email-id {
          font-weight: 500;
          color: #606266;
        }

        .email-box {
          color: #909399;
        }

        .email-actions {
          margin-left: auto;
        }
      }
    }

    .email-content {
      margin-left: 24px; // 对齐checkbox的缩进

      .sender-info {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 4px;
        font-size: 13px;

        .sender-name {
          font-weight: 700;
          color: #333;
        }

        .sender-email {
          color: #909399;
        }

        .send-date {
          margin-left: auto;
          color: #909399;
          font-size: 12px;
        }
      }

      .email-title {
        font-size: 14px;
        font-weight: 700;
        color: #333;
        margin-bottom: 4px;
        line-height: 1.3;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .email-body {
        font-size: 12px;
        color: #666;
        height: 18px;
        line-height: 18px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        margin-bottom: 2px;
        
        :deep(p) {
          margin: 0;
        }
      }
    }

    .email-footer {
      margin-left: 24px; // 对齐checkbox的缩进
      margin-top: 6px;
      padding-top: 6px;
      border-top: 1px solid #f0f0f0;

      .email-stats {
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 12px;
        color: #909399;

        .message-count {
          color: #606266;
        }

        .unread-count {
          color: #f56c6c;
          font-weight: 500;
        }

        .has-attachment {
          color: #909399;
        }

        .email-status {
          margin-left: auto;
        }
      }
    }
  }
}
</style>
