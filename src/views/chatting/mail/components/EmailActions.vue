<template>
  <div class="email-actions">
    <el-button
      size="small"
      class="remark-btn"
      type="primary"
      plain
      icon="EditPen"
      @click="handleRemark"
    >
      {{ $t('btn_add_remark') }}
    </el-button>
    <el-button
      size="small"
      class="remark-btn"
      type="primary"
      plain
      icon="PriceTag"
      @click="handleEditTag"
    >
      {{ $t('text_edit_tag') }}
    </el-button>
  </div>
</template>

<script setup lang="ts">
import { useI18n } from 'vue-i18n';

const emit = defineEmits<{
  (event: 'remark'): void;
  (event: 'edit-tag'): void;
}>();

const { t: $t } = useI18n();

const handleRemark = () => {
  emit('remark');
};

const handleEditTag = () => {
  emit('edit-tag');
};
</script>

<style lang="scss" scoped>
.email-actions {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 10px;

  .remark-btn {
    margin-right: 8px;
    margin-bottom: 8px;
  }
}
</style>
