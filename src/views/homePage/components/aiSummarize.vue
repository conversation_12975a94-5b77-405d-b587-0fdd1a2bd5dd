<template>
  <el-dialog
    v-model="value"
    width="600px"
    :title="$t('btn_ai_summarize')"
    :destroy-on-close="true"
    :before-close="close"
    :close-on-click-modal="false"
  >
    <div v-loading="loading" :element-loading-text="$t('text_ai_operation') + '...'">
      <div class="content-box" v-html="form.content"></div>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { aiSummary } from '@/api/overview';
import { useI18n } from 'vue-i18n';
const { t: $t } = useI18n();
interface AssignProps {
  visible: boolean;
  ticketId: number;
}
interface Form {
  content: string;
}
const props = withDefaults(defineProps<AssignProps>(), {
  visible: false,
  ticketId: 0,
});
const emit = defineEmits<{
  (event: 'update:visible', value: boolean): void;
}>();
const value = computed({
  get: () => props.visible,
  set: (val: boolean) => emit('update:visible', val),
});
const close = () => {
  value.value = false;
};

const form = reactive<Form>({
  content: '',
});
const loading = ref<boolean>(true);
const aiSummaryContent = async () => {
  try {
    form.content = await aiSummary({ ticket_id: props.ticketId });
  } catch (err) {
    console.log(err);
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  aiSummaryContent();
});
</script>

<style lang="scss" scoped>
.content-box {
  min-height: 200px;
  white-space: pre-wrap;
  line-height: 30px;
}
</style>
