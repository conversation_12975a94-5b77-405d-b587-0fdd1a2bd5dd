<template>
  <el-dialog
    v-model="value"
    width="600px"
    :title="$t('text_quote_template')"
    :destroy-on-close="true"
    :before-close="close"
    :close-on-click-modal="false"
  >
    <el-form class="form" label-position="top" :model="form" ref="formRef" v-loading="loading">
      <el-form-item prop="label_id">
        <el-select
          v-model="form.moduleName"
          :placeholder="$t('place_select')"
          :reserve-keyword="false"
          clearable
          filterable
          @change="changeHandle"
        >
          <el-option
            v-for="(v, index) in tempOpts"
            :key="index"
            :label="v.label"
            :value="{ label: v.label, value: v.value }"
          ></el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="close">{{ $t('btn_cancel') }}</el-button>
      <el-button type="primary" @click="submit">{{ $t('text_confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts">
import { useI18n } from 'vue-i18n';
import type { FormInstance } from 'element-plus';
import { tempLibOptions } from '@/api/templateLib';
export default defineComponent({
  name: 'OverviewEditTemp',
  components: {},
});
interface AssignProps {
  visible: boolean;
  projectName: string;
}
interface Form {
  moduleName: string;
  content: string;
}
</script>
<script setup lang="ts">
// base
const { t: $t } = useI18n();
const props = withDefaults(defineProps<AssignProps>(), {
  visible: false,
  projectName: '',
});
const emit = defineEmits<{
  (event: 'update:visible', value: boolean): void;
  (event: 'success', value: string): void;
}>();
const value = computed({
  get: () => props.visible,
  set: (val: boolean) => emit('update:visible', val),
});
const close = () => {
  value.value = false;
};
// business
const loading = ref(false);
const formRef = ref<FormInstance>();
const form = reactive<Form>({
  moduleName: '',
  content: '',
});

const submit = () => {
  emit('success', form.content);
  close();
};
const changeHandle = (params: { value: string; label: string }) => {
  const { value, label } = params;
  form.moduleName = label;
  form.content = value;
};
// 清洗html标签
// const stripTags = (htmlContent: any) => {
//   const doc = new DOMParser().parseFromString(htmlContent, 'text/html')
//   return doc.body.textContent || ""
// }
// edit
const tempOpts = ref<Array<{ label: string; value: string }>>([]);
onMounted(() => {
  // 获取模板数据
  tempLibOptions({
    project: props.projectName,
  }).then((res: any) => {
    tempOpts.value = res;
  });
});
</script>
