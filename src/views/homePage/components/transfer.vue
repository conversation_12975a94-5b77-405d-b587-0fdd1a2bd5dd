<template>
  <el-dialog
    v-model="value"
    width="600px"
    :title="$t('text_transfer_op')"
    :destroy-on-close="true"
    :before-close="close"
    :close-on-click-modal="false"
  >
    <el-form
      class="form"
      label-position="top"
      :model="form"
      :rules="rules"
      ref="formRef"
      v-loading="loading"
    >
      <el-form-item :label="$t('text_user_value')" prop="acceptor">
        <el-select
          v-model="form.acceptor"
          :placeholder="$t('place_select')"
          :reserve-keyword="false"
          filterable
          clearable
        >
          <el-option
            v-for="(v, index) in csList"
            :key="index"
            :label="v.account"
            :value="v.account"
          ></el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="close">{{ $t('btn_cancel') }}</el-button>
      <el-button type="primary" @click="submit">{{ $t('text_confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts">
import { useI18n } from 'vue-i18n';
import type { FormInstance, FormRules } from 'element-plus';
import { ticketTransfer } from '@/api/overview';
import { getAcceptorList } from '@/api/assignConfig';
export default defineComponent({
  name: 'OverviewTransfer',
  components: {
    ElMessage,
  },
});
interface AssignProps {
  visible: boolean;
  ticketId: number;
}
interface Form {
  ticket_id?: number;
  acceptor: string;
}
</script>
<script setup lang="ts">
// base
const { t: $t } = useI18n();
const props = withDefaults(defineProps<AssignProps>(), {
  visible: false,
  ticketId: 0,
});
const emit = defineEmits<{
  (event: 'update:visible', value: boolean): void;
  (event: 'success'): void;
}>();
const value = computed({
  get: () => props.visible,
  set: (val: boolean) => emit('update:visible', val),
});
const close = () => {
  value.value = false;
};
// business
const csList = ref([]);
const getCsList = async () => {
  const res = await getAcceptorList({});
  csList.value = res;
};
getCsList();
const loading = ref(false);
const formRef = ref<FormInstance>();
const form = reactive<Form>({
  acceptor: '',
});
const rules = reactive<FormRules>({
  acceptor: [{ required: true, message: $t('place_select'), trigger: 'change' }],
});

const submit = () => {
  formRef.value!.validate(async valid => {
    if (!valid || loading.value) return;
    loading.value = true;
    try {
      await ticketTransfer(form);
      ElMessage.success($t('text_success'));
      emit('success');
      close();
    } catch (error) {
      console.log(error);
    } finally {
      loading.value = false;
    }
  });
};

// edit
onMounted(() => {
  form.ticket_id = props.ticketId;
});
</script>
