<template>
  <div class="page-view-wapper">
    <div class="search-box">
      <el-form ref="searchFormRef" :inline="true" :model="searchForm" size="small">
        <!-- 报表维度 -->
        <el-form-item :label="`${$t('报表维度')}：`" prop="stat_type">
          <el-select
            v-model="searchForm.stat_type"
            :placeholder="$t('place_select')"
            style="width: 150px"
            @change="changeTypeHandle"
          >
            <el-option
              v-for="(v, index) in enumList.SurveyStatType"
              :key="index"
              :label="v.name"
              :value="v.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <!-- 游戏 -->
        <el-form-item :label="`${$t('text_game')}：`" prop="project">
          <el-select
            v-model="searchForm.project"
            :placeholder="$t('place_select')"
            multiple
            collapse-tags
            collapse-tags-tooltip
            clearable
            filterable
            :reserve-keyword="false"
            style="width: 150px"
          >
            <el-option
              v-for="(v, index) in gameList"
              :key="index"
              :label="v.app_name"
              :value="v.game_project"
            ></el-option>
          </el-select>
        </el-form-item>
        <!-- 评价日期 -->
        <el-form-item :label="`${$t('text_date')}：`" prop="date">
          <el-date-picker
            v-model="searchForm.date"
            type="daterange"
            range-separator="~"
            :start-placeholder="$t('start_date')"
            :end-placeholder="$t('end_date')"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <!-- 评价对象 -->
        <el-form-item :label="`${$t('评价对象')}：`" prop="evaluation_target">
          <el-select
            v-model="searchForm.evaluation_target"
            :placeholder="$t('place_select')"
            multiple
            collapse-tags
            collapse-tags-tooltip
            clearable
            style="width: 150px"
          >
            <el-option
              v-for="(v, index) in enumList.SurveyQstType"
              :key="index"
              :label="v.name"
              :value="v.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <!-- 处理人 -->
        <el-form-item :label="`${$t('text_processed_by')}：`" prop="operator">
          <el-select
            v-model="searchForm.operator"
            :placeholder="$t('place_select')"
            multiple
            collapse-tags
            collapse-tags-tooltip
            filterable
            :reserve-keyword="false"
            clearable
            style="width: 150px"
          >
            <el-option
              v-for="(v, index) in csList"
              :key="index"
              :label="v.account"
              :value="v.account"
            ></el-option>
          </el-select>
        </el-form-item>
        <!-- 维护人 -->
        <el-form-item :label="`${$t('维护人')}：`" prop="maintainer">
          <el-select
            v-model="searchForm.maintainer"
            :placeholder="$t('place_select')"
            multiple
            collapse-tags
            collapse-tags-tooltip
            filterable
            :reserve-keyword="false"
            clearable
            style="width: 150px"
          >
            <el-option
              v-for="(v, index) in csList"
              :key="index"
              :label="v.account"
              :value="v.account"
            ></el-option>
          </el-select>
        </el-form-item>
        <!-- 查询 -->
        <el-form-item>
          <el-button type="primary" icon="Search" plain @click="searchHandle">{{
            $t('btn_search')
          }}</el-button>
        </el-form-item>
        <!-- 下载 -->
        <el-form-item>
          <el-button
            type="primary"
            icon="Download"
            @click="donwLoad"
            v-has="'satisfaction:download'"
            :loading="progState"
            :disabled="progState"
            >{{ $t('btn_download') }}
            <span v-if="progState">{{ progressNum + '%' }}</span>
          </el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="page-content-box">
      <ops-table
        ref="_table"
        :data-api="satisfactionData"
        :params="searchForm"
        :border="true"
        @sort-change="handleSort"
      >
        <el-table-column
          label="日期"
          prop="evaluation_date"
          v-if="searchForm.stat_type === 1"
        ></el-table-column>
        <el-table-column
          label="游戏"
          prop="project"
          v-if="searchForm.stat_type === 2"
        ></el-table-column>
        <el-table-column
          label="处理人"
          prop="operator"
          v-if="searchForm.stat_type === 3"
        ></el-table-column>
        <el-table-column
          v-for="(item, index) in columns"
          :key="item.prop + index"
          :prop="item.prop"
          :label="item.label"
          :width="item.width ? item.width : 'auto'"
          :align="item.align ? item.align : 'center'"
          :sortable="true"
          :show-overflow-tooltip="item.showTooltip"
        >
          <template #default="scope">
            {{ scope.row[item.prop] || '-' }}
          </template>
        </el-table-column>
      </ops-table>
    </div>
  </div>
</template>

<script lang="ts">
import type { FormInstance } from 'element-plus';
import { satisfactionData, satisfactionExport } from '@/api/report';
import { getAcceptorList } from '@/api/assignConfig';
import { useI18n } from 'vue-i18n';
import { useEnumStore, useAppStore } from '@/stores';
export default defineComponent({
  name: 'TagLibraryConfig',
  components: {},
});
interface SearchForm {
  project: string[];
  date: string[];
  operator: string[];
  evaluation_target: string[];
  maintainer: string[];
  stat_type: number;
}
interface Column {
  prop: string;
  label: string;
  width?: string;
  showTooltip?: boolean;
  sortable?: boolean | string;
}
</script>
<script setup lang="ts">
const { t: $t } = useI18n();
const gameList = computed(() => useEnumStore().gameList);
const enumList = computed(() => useEnumStore().enumList);
const personList = ref([]);
// 按钮携带下载进度
const progressNum = computed(() => useAppStore().progressNum);
const progState = ref<boolean>(false);
watch(progressNum, n => {
  progState.value = n < 100 && n > -1 ? true : false;
});
const _table = ref();
const searchFormRef = ref<FormInstance>();
const searchForm = ref<SearchForm>({
  project: [],
  date: [],
  operator: [],
  evaluation_target: [],
  maintainer: [],
  stat_type: 1,
});

const columns = computed((): Column[] => {
  return [
    { prop: '1', label: $t('text_one_star') },
    { prop: '2', label: $t('text_two_star') },
    { prop: '3', label: $t('text_three_star') },
    { prop: '4', label: $t('text_four_star') },
    { prop: '5', label: $t('text_five_star') },
    { prop: 'all', label: $t('text_scor_number') },
    { prop: 'gt3_rate', label: $t('text_four_and_five_star_account') },
  ];
});
const handleSort = (val: object) => {
  console.log(val, '----table sort');
};
const csList = ref<Record<string, string>[]>([]);
const getCsList = async () => {
  const res = await getAcceptorList({});
  csList.value = res;
  // csList.value.forEach((item) => {
  //   columns.value.push({ prop: item.account, label: item.account })
  // })
};
getCsList();
// 查询
const searchHandle = () => {
  _table.value.getData();
};
// 下载
const donwLoad = () => {
  satisfactionExport(searchForm.value);
};
const changeTypeHandle = (val: string) => {
  searchHandle();
};
</script>
<style lang="scss">
.header_content {
  height: 50px;
  position: relative;
}
.triangle_top {
  position: absolute;
  bottom: 0;
  left: 0;
}
.triangle_bottom {
  position: absolute;
  top: 0;
  right: 0;
}
.header_line {
  width: 0.5px;
  height: 128px;
  transform: rotate(-56deg);
  transform-origin: top;
  background-color: rgb(205 205 205);
}
</style>
