<template>
  <el-dialog
    v-model="value"
    width="600px"
    :title="$t('text_question_type_manage')"
    :destroy-on-close="true"
    :before-close="close"
    :close-on-click-modal="false"
    align-center
    class="category-dialog"
  >
    <el-form
      class="form"
      size="small"
      :inline="true"
      label-position="right"
      :model="form"
      :rules="rules"
      ref="dcFormRef"
      v-loading="loading"
    >
      <!-- 游戏 -->
      <el-form-item :label="$t('text_game')" prop="game_project">
        <el-select
          v-model="form.game_project"
          :placeholder="$t('place_select')"
          clearable
          @change="handleGame"
          style="width: 250px"
        >
          <el-option
            v-for="(v, index) in gameList"
            :key="index"
            :label="v.app_name"
            :value="v.game_project"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button size="small" type="primary" icon="Plus" plain @click="addFirstLevel">{{
          $t('text_one_type')
        }}</el-button>
      </el-form-item>
    </el-form>
    <div class="page-tag-content">
      <div class="menu-wrap" v-loading="tagsLoading">
        <el-tree
          class="category-tree"
          :data="treeData"
          :default-expanded-keys="[0]"
          node-key="id"
          ref="tree"
        >
          <template #default="{ node, data }">
            <div class="custom-tree-node">
              <div>{{ data.label }}</div>
              <div @click.stop>
                <el-button
                  v-if="data.level !== 0"
                  type="primary"
                  text
                  size="small"
                  icon="EditPen"
                  @click="() => edit(node, data)"
                >
                </el-button>
                <el-popconfirm
                  v-if="data.level !== 0"
                  confirm-button-text="确认"
                  cancel-button-text="取消"
                  icon="InfoFilled"
                  icon-color="red"
                  title="确认删除"
                  class="ml-10"
                  @confirm="del(node, data)"
                >
                  <template #reference>
                    <el-button class="delete-btn" type="danger" text size="small" icon="Delete">
                    </el-button>
                  </template>
                </el-popconfirm>
                <el-button
                  :disabled="data.level === 3"
                  type="primary"
                  text
                  class="ml-10"
                  size="small"
                  icon="Plus"
                  @click="() => append(node, data)"
                  >{{ $t('btn_next_level') }}
                </el-button>
              </div>
            </div>
          </template>
        </el-tree>
      </div>
      <el-dialog
        v-model="titleDialogVisible"
        :title="
          Object.keys(props.editData).length > 0
            ? $t('text_edit_question_type')
            : $t('text_add_question_type')
        "
        width="400px"
        :destroy-on-close="true"
        :before-close="closeDialog"
        :close-on-click-modal="false"
        align-center
      >
        <el-form :model="tagForm" :rules="rulesQues" ref="formRef" v-loading="loading">
          <el-form-item :label="$t('text_questiuon_type_name')" prop="category">
            <el-input v-model="tagForm.category" clearable :placeholder="$t('place_input')" />
          </el-form-item>
        </el-form>
        <template #footer>
          <el-button @click="closeDialog">{{ $t('btn_cancel') }}</el-button>
          <el-button type="primary" :disabled="!tagForm.category" @click="submit">{{
            $t('btn_ok')
          }}</el-button>
        </template>
      </el-dialog>
    </div>
  </el-dialog>
</template>

<script lang="ts">
import { useI18n } from 'vue-i18n';
import type { FormInstance, FormRules } from 'element-plus';
import { useEnumStore } from '@/stores';
import { questionTypeList, questionTypeAdd, questionTypeEdit, questionTypeDel } from '@/api/report';
export default defineComponent({
  name: 'EditTagLibConfig',
  components: {
    ElMessage,
  },
});
interface EditQuestionProps {
  visible: boolean;
  editData?: Record<string, unknown>;
  catType: number;
}
interface Form {
  game_project: string;
}
interface TagForm {
  category: string;
}
interface Params {
  category?: string;
  one_level?: number;
  second_level?: number;
  cat_type?: number;
  cat_level?: number;
  cat_id?: number;
}
interface TreeNode {
  id: number;
  label: string;
  level?: number;
  children: any[];
}
</script>
<script setup lang="ts">
// base
const { t: $t } = useI18n();
const props = withDefaults(defineProps<EditQuestionProps>(), {
  visible: false,
  editData: () => ({}),
});
const emit = defineEmits<{
  (event: 'update:visible', value: boolean): void;
  (event: 'success'): void;
}>();
const value = computed({
  get: () => props.visible,
  set: (val: boolean) => emit('update:visible', val),
});
const close = () => {
  value.value = false;
};
const gameList = computed(() => useEnumStore().gameList);
const tagsLoading = ref(false);
const treeData = ref<TreeNode[]>([]);
const editType = ref(1); // 1-新增 2-编辑
const form = reactive<Form>({
  game_project: '',
});
const tagForm = reactive<TagForm>({
  category: '',
});
const loading = ref(false);
const formRef = ref<FormInstance>();
const rules = reactive<FormRules>({
  game_project: [{ required: true, message: $t('place_select'), trigger: 'change' }],
});
const rulesQues = reactive<FormRules>({
  question_name: [{ required: true, message: $t('place_input'), trigger: 'blur' }],
});
const titleDialogVisible = ref(false);
const closeDialog = () => {
  tagForm.category = '';
  titleDialogVisible.value = false;
};
const getGame = ref('');
// 获取标签配置列表
const getTagConfigList = () => {
  if (form.game_project) {
    tagsLoading.value = true;
    treeData.value = [];
    questionTypeList({
      project: getGame.value,
      cat_type: props.catType,
    })
      .then((res: any) => {
        treeData.value = res.data;
      })
      .finally(() => {
        tagsLoading.value = false;
      });
  }
};
// 获取游戏
const handleGame = (val: string) => {
  getGame.value = val;
  getTagConfigList();
};
const newParams = ref({} as Params);
const addFirstLevel = () => {
  titleDialogVisible.value = true;
  editType.value = 1;
  newParams.value = {
    cat_level: 1,
    one_level: 0,
  };
};
const append = (node: any, data: object) => {
  titleDialogVisible.value = true;
  editType.value = 1;
  newParams.value = {
    cat_level: node.data.level + 1,
    one_level: node.data.id,
  };
  if (node.data.level === 2) {
    newParams.value.second_level = node.data.id;
  }
};
const edit = (node: any, data: object) => {
  titleDialogVisible.value = true;
  editType.value = 2;
  newParams.value = {
    cat_id: node.data.id,
    cat_level: node.data.level,
  };
  tagForm.category = node.data.label;
};
const del = (node: any, data: object) => {
  if (tagsLoading.value) return;
  tagsLoading.value = true;
  questionTypeDel({ cat_id: node.data.id })
    .then(() => {
      getTagConfigList();
      ElMessage.success($t('text_del_success'));
    })
    .catch(() => {})
    .finally(() => {
      tagsLoading.value = false;
    });
};
const submit = () => {
  formRef.value!.validate(async valid => {
    if (!valid || loading.value) return;
    loading.value = true;
    try {
      if (editType.value === 1) {
        await questionTypeAdd({
          ...tagForm,
          ...newParams.value,
          project: form.game_project,
          cat_type: props.catType,
        });
      } else {
        await questionTypeEdit({
          category: tagForm.category,
          ...newParams.value,
        });
      }
      ElMessage.success($t('text_add_success'));
      closeDialog();
      getTagConfigList();
    } catch (error) {
      console.log(error);
    } finally {
      loading.value = false;
    }
  });
};
onMounted(() => {});
</script>

<style lang="scss" scoped>
.ml-10 {
  margin-left: 10px;
}
.category-dialog {
  &:deep(.el-dialog__body) {
    padding: 20px;
    min-height: 300px;
    max-height: 600px;
    overflow: auto;
  }
  .menu-wrap {
    margin-top: 10px;
  }
  &:deep(.el-tree-node__content) {
    height: 30px;
  }
  .custom-tree-node {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    padding-right: 8px;
    &:deep(.el-button--small) {
      padding: 0;
    }
  }
  .delete-btn {
    &.el-button--text {
      color: #f56c6c;
      &.is-active,
      &:active {
        color: #dd6161;
      }
      &:focus,
      &:hover {
        color: #f78989;
      }
    }
  }
}
// .menu-wrap {
//   &:deep(.el-tree-node__content) {
//     height: 30px;
//   }
//   .custom-tree-node {
//     flex: 1;
//     display: flex;
//     align-items: center;
//     justify-content: space-between;
//     font-size: 14px;
//     padding-right: 8px;
//     .ml-10 {
//       margin-left: 10px;
//     }
//   }
// }
</style>
