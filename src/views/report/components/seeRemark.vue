<template>
  <el-dialog
    v-model="value"
    width="600px"
    :title="$t('text_remark_info')"
    :destroy-on-close="true"
    :before-close="close"
    :close-on-click-modal="false"
  >
    <div v-if="props.remarkList">
      <div class="dialogue-content">
        <p class="content-text" v-html="props.remarkList"></p>
      </div>
    </div>
    <el-empty v-else :description="$t('info_no_data')" />
    <template #footer>
      <el-button @click="close">{{ $t('btn_cancel') }}</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts">
import { useI18n } from 'vue-i18n';
export default defineComponent({
  name: 'Dialogue',
  components: {},
});
interface dialogueProps {
  visible: boolean;
  paramsData?: string[];
  remarkList: string;
}
</script>
<script setup lang="ts">
// base
const { t: $t } = useI18n();
const props = withDefaults(defineProps<dialogueProps>(), {
  visible: false,
  paramsData: () => [],
});
const emit = defineEmits<{
  (event: 'update:visible', value: boolean): void;
  (event: 'success'): void;
}>();
const value = computed({
  get: () => props.visible,
  set: (val: boolean) => emit('update:visible', val),
});
const remarkList = ref();
const close = () => {
  value.value = false;
};
onMounted(() => {});
</script>

<style lang="scss" scoped>
.dialogue-content {
  height: 450px;
  overflow-y: auto;
  padding-right: 5px;
  .content-text {
    width: fit-content;
    line-height: 1.375rem;
    word-wrap: break-word;
    word-break: break-word;
    margin-bottom: 10px;
    padding: 5px 10px;
    border-radius: 8px;
    color: #23262a;
    font-size: 14px;
    background-color: #d4e3ff;
    &:deep(p) {
      margin-block-start: 0.3em !important;
      margin-block-end: 0.3em !important;
      img {
        width: 100%;
      }
    }
  }
}
</style>
