export const TREND_DATA = {
  tooltip: {
    trigger: 'axis',
  },
  legend: {
    data: [],
    top: '2%',
  },
  grid: {
    left: '1%',
    right: '1%',
    bottom: '13%',
    containLabel: true,
  },
  toolbox: {
    right: '12px',
    top: '1%',
    feature: {
      myDownload: {
        show: true,
        title: '下载数据',
        icon: 'path://M160 832h704a32 32 0 1 1 0 64H160a32 32 0 1 1 0-64m384-253.696 236.288-236.352 45.248 45.248L508.8 704 192 387.2l45.248-45.248L480 584.704V128h64z',
        iconStyle: {
          // color: '#80bda7',
          // borderColor: '#4aa181',
          scale: 1.5,
        },
        emphasis: {
          iconStyle: {
            color: '#80bda7', // hover 时的图标颜色
            borderColor: '#4aa181', // hover 时的边框颜色
            // ...其他 hover 状态样式...
          },
          textStyle: {
            color: '#4aa181', // hover 时的文字颜色
          },
        },
      },
    },
  },
  xAxis: {
    type: 'category',
    boundaryGap: true,
  },
  yAxis: {
    type: 'value',
  },
  dataZoom: [
    {
      type: 'slider',
      start: 0,
      end: 100,
      left: '6%',
      right: '6%',
      height: '20px',
      top: '92%',
    },
  ],
  series: [],
};
