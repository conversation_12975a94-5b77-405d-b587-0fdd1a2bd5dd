<template>
  <div class="page-view-wapper">
    <div class="search-box">
      <el-form :inline="true" :model="searchForm" size="small">
        <!-- 游戏 -->
        <el-form-item :label="`${$t('text_game')}：`">
          <el-select
            v-model="searchForm.project"
            :placeholder="$t('place_select')"
            multiple
            collapse-tags
            collapse-tags-tooltip
            clearable
            filterable
            :reserve-keyword="false"
            style="width: 180px"
          >
            <el-option
              v-for="(v, index) in gameList"
              :key="index"
              :label="v.app_name"
              :value="v.game_project"
            ></el-option>
          </el-select>
        </el-form-item>
        <!-- 日期 -->
        <el-form-item :label="`${$t('text_date')}：`">
          <el-date-picker
            v-model="searchForm.date"
            type="daterange"
            range-separator="~"
            :start-placeholder="$t('start_date')"
            :end-placeholder="$t('end_date')"
            style="width: 200px"
            value-format="YYYY-MM-DD"
            format="YYYY-MM-DD"
            clearable
          >
          </el-date-picker>
        </el-form-item>
        <!-- 处理人 -->
        <el-form-item :label="`${$t('text_processed_by')}：`">
          <el-select
            v-model="searchForm.operator"
            :placeholder="$t('place_select')"
            multiple
            collapse-tags
            collapse-tags-tooltip
            :reserve-keyword="false"
            filterable
            clearable
            style="width: 180px"
          >
            <el-option
              v-for="(v, index) in csList"
              :key="index"
              :label="v.account"
              :value="v.account"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" plain @click="searchHandle">{{
            $t('btn_search')
          }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            icon="Download"
            @click="donwLoad"
            v-has="'interaction:download'"
            :loading="progState"
            :disabled="progState"
            >{{ $t('text_data_export') }}
            <span v-if="progState">{{ progressNum + '%' }}</span>
          </el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="page-content-box">
      <ops-table
        ref="_table"
        :data-api="interactionData"
        :params="searchForm"
        tooltip-effect="dark"
        :border="true"
      >
        <el-table-column
          :width="100"
          fixed="left"
          :resizable="false"
          prop="row_name"
          :label="$t('text_date')"
          :align="'center'"
        >
          <template #header>
            <div class="header_content">
              <div class="triangle_top">{{ $t('text_date') }}</div>
              <div class="triangle_bottom">{{ $t('text_cs') }}</div>
              <div class="header_line"></div>
            </div>
          </template>
          <template #default="scope">
            {{ scope.row.row_name === 'sum' ? $t('text_sum') : scope.row.row_name }}
          </template>
        </el-table-column>
        <el-table-column
          prop="sum_count"
          :label="$t('text_sum')"
          :align="'center'"
          fixed="left"
        ></el-table-column>
        <el-table-column
          v-for="(item, index) in columns"
          :key="item.prop + index"
          :prop="item.prop"
          :label="item.label"
          :width="item.width ? item.width : 'auto'"
          :align="item.align ? item.align : 'center'"
        >
          <template #default="scope">
            {{ scope.row[item.prop] || '-' }}
          </template>
        </el-table-column>
      </ops-table>
    </div>
  </div>
</template>

<script lang="ts">
import { interactionData, interactionExport } from '@/api/report';
import { useI18n } from 'vue-i18n';
import { getAcceptorList } from '@/api/assignConfig';
import { useEnumStore, useAppStore } from '@/stores';
export default defineComponent({
  name: 'Interaction',
  components: {},
});
interface SearchForm {
  project: string[];
  date: string[];
  operator: string[];
}
</script>
<script setup lang="ts">
const { t: $t } = useI18n();
const gameList = computed(() => useEnumStore().gameList);
const _table = ref();
const searchForm = ref<SearchForm>({
  project: [],
  date: [],
  operator: [],
});
const columns = ref<Column[]>([{ prop: 'others', label: $t('text_others') }]);
const csList = ref<Record<string, string>[]>([]);
const getCsList = async () => {
  const res = await getAcceptorList({});
  csList.value = res;
  csList.value.forEach(item => {
    columns.value.push({ prop: item.account, label: item.account });
  });
};
getCsList();
// 按钮携带下载进度
const progressNum = computed(() => useAppStore().progressNum);
const progState = ref<boolean>(false);
watch(progressNum, n => {
  progState.value = n < 100 && n > -1 ? true : false;
});

const searchHandle = () => {
  if (searchForm.value.operator.length > 0) {
    columns.value = [{ prop: 'others', label: $t('text_others') }];
    searchForm.value.operator.forEach(item => {
      columns.value.push({ prop: item, label: item });
    });
  }
  if (searchForm.value.operator.length === 0) {
    columns.value = [{ prop: 'others', label: $t('text_others') }];
    csList.value.forEach(item => {
      columns.value.push({ prop: item.account, label: item.account });
    });
  }
  _table.value.getData();
};
const donwLoad = () => {
  interactionExport(searchForm.value);
};
</script>
<style lang="scss" scoped>
.header_content {
  height: 50px;
  position: relative;
}
.triangle_top {
  position: absolute;
  bottom: 0;
  left: 0;
}
.triangle_bottom {
  position: absolute;
  top: 0;
  right: 0;
}
.header_line {
  width: 0.5px;
  height: 128px;
  transform: rotate(-56deg);
  transform-origin: top;
  background-color: rgb(205 205 205);
}
</style>
