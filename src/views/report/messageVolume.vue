<template>
  <div class="page-view-wapper">
    <div class="search-box">
      <el-form :inline="true" :model="searchForm" size="small">
        <!-- 游戏 -->
        <el-form-item :label="`${$t('text_game')}：`">
          <el-select
            v-model="searchForm.project"
            :placeholder="$t('place_select')"
            multiple
            collapse-tags
            collapse-tags-tooltip
            clearable
            filterable
            :reserve-keyword="false"
            style="width: 180px"
            @change="projectChangeHandle"
          >
            <el-option
              v-for="(v, index) in gameList"
              :key="index"
              :label="v.app_name"
              :value="v.game_project"
            ></el-option>
          </el-select>
        </el-form-item>
        <!-- 日期 -->
        <el-form-item :label="`${$t('text_date')}：`">
          <el-date-picker
            v-model="searchForm.date"
            type="daterange"
            range-separator="~"
            :start-placeholder="$t('start_date')"
            :end-placeholder="$t('end_date')"
            style="width: 200px"
            value-format="YYYY-MM-DD"
            format="YYYY-MM-DD"
            clearable
          >
          </el-date-picker>
        </el-form-item>
        <!-- 处理人 -->
        <el-form-item :label="`${$t('text_processed_by')}：`">
          <el-select
            v-model="searchForm.operator"
            :placeholder="$t('place_select')"
            multiple
            collapse-tags
            collapse-tags-tooltip
            filterable
            :reserve-keyword="false"
            clearable
            style="width: 180px"
          >
            <el-option
              v-for="(v, index) in csList"
              :key="index"
              :label="v.account"
              :value="v.account"
            ></el-option>
          </el-select>
        </el-form-item>
        <!-- 玩家 -->
        <el-form-item :label="`${$t('text_player')}：`">
          <el-select
            v-model="searchForm.uid"
            :placeholder="$t('place_select')"
            multiple
            collapse-tags
            filterable
            :reserve-keyword="false"
            collapse-tags-tooltip
            clearable
            style="width: 180px"
            :disabled="!(searchForm.project && searchForm.project.length > 0)"
          >
            <el-option
              v-for="(v, index) in playerList"
              :key="index"
              :label="v"
              :value="v"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" plain @click="searchHandle">{{
            $t('btn_search')
          }}</el-button>
        </el-form-item>
        <!-- <el-form-item>
          <el-button size="small" type="primary" icon="Plus" plain @click="addRelationHandle">{{ $t('text_new_add')
            }}</el-button>
        </el-form-item> -->
        <el-form-item>
          <el-button
            type="primary"
            icon="Download"
            @click="donwLoad"
            v-has="'messageVolume:download'"
            :loading="progState"
            :disabled="progState"
            >{{ $t('text_data_export') }}
            <span v-if="progState">{{ progressNum + '%' }}</span>
          </el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="page-content-box" id="hf" style="overflow: auto">
      <div class="table-hf">
        <ops-table
          ref="_table1"
          :data-api="timeMsgData"
          :params="searchForm"
          tooltip-effect="dark"
          class="custom-table"
        >
          <el-table-column
            :width="100"
            fixed="left"
            :resizable="false"
            prop="row_name"
            :label="$t('text_date')"
            :align="'center'"
          >
            <template #default="scope">
              {{ scope.row.row_name === 'sum_count' ? $t('text_sum') : scope.row.row_name }}
            </template>
          </el-table-column>
          <el-table-column
            v-for="(item, index) in columns"
            :key="item.prop + index"
            :prop="item.prop"
            :label="item.label"
            :width="item.width ? item.width : 'auto'"
            :align="item.align ? item.align : 'center'"
          >
            <template #default="scope">
              {{ scope.row[item.prop] || '-' }}
            </template>
          </el-table-column>
        </ops-table>
      </div>
      <div class="table-hf">
        <ops-table
          ref="_table2"
          :data-api="operatorMsgData"
          :params="searchForm"
          tooltip-effect="dark"
          class="custom-table"
        >
          <el-table-column
            :width="100"
            fixed="left"
            :resizable="false"
            prop="row_name"
            :label="$t('text_cs')"
            :align="'center'"
          >
            <template #default="scope">
              {{ scope.row.row_name === 'sum_count' ? $t('text_sum') : scope.row.row_name }}
            </template>
          </el-table-column>
          <el-table-column
            v-for="(item, index) in columns"
            :key="item.prop + index"
            :prop="item.prop"
            :label="item.label"
            :width="item.width ? item.width : 'auto'"
            :align="item.align ? item.align : 'center'"
          >
            <template #default="scope">
              {{ scope.row[item.prop] || '-' }}
            </template>
          </el-table-column>
        </ops-table>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { getPlayerUid, timeMsgData, operatorMsgData, msgDetailExport } from '@/api/report';
import { getAcceptorList } from '@/api/assignConfig';
import { useI18n } from 'vue-i18n';
import { useEnumStore, useAppStore } from '@/stores';
export default defineComponent({
  name: 'MessageVolume',
  components: {},
});
interface SearchForm {
  project: string[];
  date: string[];
  operator: string[];
  uid: number[];
}
</script>
<script setup lang="ts">
const { t: $t } = useI18n();
const gameList = computed(() => useEnumStore().gameList);
const _table1 = ref();
const _table2 = ref();
const searchForm = ref<SearchForm>({
  project: [],
  date: [],
  operator: [],
  uid: [],
});
const columns = computed((): Column[] => {
  return [
    { prop: 'player_message_count', label: $t('text_player_msg_num') },
    { prop: 'service_message_count', label: $t('text_cs_msg_num') },
  ];
});

const progressNum = computed(() => useAppStore().progressNum);
const progState = ref<boolean>(false);
watch(progressNum, n => {
  progState.value = n < 100 && n > -1 ? true : false;
});

const csList = ref<Record<string, string>[]>([]);
const getCsList = async () => {
  const res = await getAcceptorList({});
  csList.value = res;
};
getCsList();

const playerList = ref([]);
const getPlayerList = async () => {
  const res = await getPlayerUid({ project: searchForm.value.project });
  playerList.value = res;
};

const projectChangeHandle = (v: string[]) => {
  searchForm.value.uid = [];
  if (v.length > 0) {
    getPlayerList();
  } else {
    playerList.value = [];
  }
};

const searchHandle = () => {
  _table1.value.getData();
  _table2.value.getData();
};

const donwLoad = () => {
  msgDetailExport(searchForm.value);
};
</script>
<style scoped lang="scss">
#hf {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: space-between;
}
.table-hf {
  width: 49.5%;
  height: 100%;
}
</style>
