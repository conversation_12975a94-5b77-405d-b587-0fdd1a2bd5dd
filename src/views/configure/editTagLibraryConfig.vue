<template>
  <div class="page-tag-content">
    <div class="top-box">
      <span class="pb-style">{{ route.query.lib_name }}</span>
      <span>{{ $t('text_configure') }}</span>
    </div>
    <div class="tag-btn">
      <el-button type="primary" @click="addFirstLevel">{{ $t('btn_first_level_tag') }}</el-button>
    </div>
    <div class="menu-wrap" v-loading="tagsLoading">
      <el-tree class="category-tree" :data="treeData" node-key="tag_id" ref="tree">
        <template #default="{ node, data }">
          <span class="custom-tree-node">
            <span>{{ data.tag_name }}</span>
            <span @click.stop>
              <el-link
                type="primary"
                class="ml-10"
                :underline="false"
                @click="() => addSameLevel(node, data)"
                >{{ $t('btn_same_level') }}</el-link
              >
              <el-link
                type="primary"
                class="ml-10"
                :underline="false"
                @click="() => addLowerLevel(node, data)"
                >{{ $t('btn_next_level') }}</el-link
              >
              <el-link
                type="primary"
                class="ml-10"
                :underline="false"
                icon="EditPen"
                @click="() => editLevel(node, data)"
              ></el-link>
              <el-popconfirm title="删除确认" @confirm="deleteLevel(node, data)">
                <template #reference>
                  <el-link type="danger" class="ml-10" :underline="false" icon="Delete"></el-link>
                </template>
              </el-popconfirm>
            </span>
          </span>
        </template>
      </el-tree>
    </div>
    <tagConfig
      v-if="tagConfigVisible"
      v-model:visible="tagConfigVisible"
      :edit-data="tagConfigData"
      @success="getTagConfigList"
    />
  </div>
</template>

<script lang="ts">
import { useI18n } from 'vue-i18n';
import { getConfigTagList, configTagDel } from '@/api/tagLib';
import tagConfig from './components/editTagConfig.vue';
import { useRoute } from 'vue-router';
export default defineComponent({
  name: 'EditTagLibConfig',
  components: {
    ElMessage,
    tagConfig,
  },
});
</script>
<script setup lang="ts">
// base
const { t: $t } = useI18n();
const tagConfigVisible = ref(false);
const tagsLoading = ref(false);
const tagConfigData = ref({});
const treeData = ref([]);
const route = useRoute();
// 获取标签配置列表
const getTagConfigList = () => {
  tagsLoading.value = true;
  getConfigTagList({ lib_id: Number(route.query.lib_id) })
    .then((res: any) => {
      treeData.value = res.data;
    })
    .finally(() => {
      tagsLoading.value = false;
    });
};
getTagConfigList();
// 添加一级标签
const addFirstLevel = () => {
  const libId = Number(route.query.lib_id);
  tagConfigData.value = {
    lib_id: libId,
    level: 1,
  };
  tagConfigVisible.value = true;
};
// 添加平级标签
const addSameLevel = (node: any, data: object) => {
  const libId = Number(route.query.lib_id);
  if (node.data.level === 1) {
    tagConfigData.value = {
      lib_id: libId,
      level: 1,
    };
  } else {
    tagConfigData.value = {
      lib_id: libId,
      pid: node.parent.data.tag_id,
      level: node.data.level,
    };
  }
  tagConfigVisible.value = true;
};
// 添加下级标签
const addLowerLevel = (node: any, data: { tag_id: number }) => {
  const libId = Number(route.query.lib_id);
  tagConfigData.value = {
    lib_id: libId,
    level: node.level + 1,
    pid: data.tag_id,
  };
  tagConfigVisible.value = true;
};
// 编辑标签
const editLevel = (node: any, data: { tag_id: number; tag_name: string }) => {
  tagConfigData.value = {
    tag_id: data.tag_id,
    tag_name: data.tag_name,
  };
  tagConfigVisible.value = true;
};
const deleteLevel = (node: any, data: { tag_id: number }) => {
  configTagDel({ tag_id: data.tag_id }).then(() => {
    ElMessage.success($t('text_del_success'));
    getTagConfigList();
  });
};
</script>

<style lang="scss" scoped>
.top-box {
  padding: 5px;
  margin-left: 20px;
  margin-top: 10px;
  border-left: 4px solid #4aa181;
  .pb-style {
    color: #4aa181;
    padding: 0 5px;
  }
}
.tag-btn {
  padding: 20px;
}

.menu-wrap {
  padding: 0 20px;
  &:deep(.el-tree-node__content) {
    height: 30px;
  }
  .custom-tree-node {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    padding-right: 8px;
    .ml-10 {
      margin-left: 10px;
    }
  }
}
</style>
