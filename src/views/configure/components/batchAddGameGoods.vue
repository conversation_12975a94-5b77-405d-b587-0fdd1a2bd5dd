<template>
  <el-dialog
    v-model="value"
    width="600px"
    :title="$t('text_batch_upload')"
    :destroy-on-close="true"
    :before-close="close"
    :close-on-click-modal="false"
  >
    <el-form
      class="form"
      label-position="top"
      size="small"
      :model="form"
      :rules="rules"
      ref="formRef"
      v-loading="loading"
    >
      <el-form-item :label="$t('text_game')" prop="project">
        <el-select v-model="form.project" :placeholder="$t('place_select')" clearable filterable>
          <el-option
            v-for="(v, index) in gameList"
            :key="index"
            :label="v.app_name"
            :value="v.game_project"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="请按照模板导入数据" prop="file_name">
        <el-upload
          ref="upload"
          :action="`/gateway/proxy/cs-api/all/v1/upload_excel`"
          accept=".xls, .xlsx"
          :headers="{ 'admin-gateway-token': token }"
          :before-upload="handleBeforeUpload"
          :on-remove="handleRemove"
          :on-success="handleSuccess"
          :file-list="fileList"
          :on-error="handleFailed"
          :on-preview="handlePreview"
          :limit="1"
          class="upload_box"
        >
          <el-button v-if="fileList.length > 0" type="primary" :disabled="true">点击上传</el-button>
          <el-button v-else type="primary">点击上传</el-button>
          <template #tip>
            <div class="el-upload__tip">
              <i class="el-icon-warning-outline"></i> 只能上传excel文件
              <el-link
                type="primary"
                @click.prevent="
                  downloadFile({
                    filename: $t('text_batch_add_gamegoods_temp') + '.xlsx',
                    url: enumList.DownloadUrl.filter(
                      (i: Record<string, string>) => i.name === 'DataPlatItemDemo'
                    )[0].value,
                  })
                "
                >下载模板</el-link
              >
            </div>
          </template>
        </el-upload>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="close">{{ $t('btn_cancel') }}</el-button>
      <el-button type="primary" @click="submit">{{ $t('text_confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts">
import { useI18n } from 'vue-i18n';
import type { FormInstance, FormRules, UploadInstance, UploadFile } from 'element-plus';
import { genFileId } from 'element-plus';
import { gameGoodsConfigUpload } from '@/api/gameGoodsConfig';
import { downloadFile } from '@/api/common';
import { useEnumStore, useUserInfoStore } from '@/stores';
export default defineComponent({
  name: 'EditTagLib',
  components: {
    ElMessage,
  },
});
interface EditTagLibProps {
  visible: boolean;
}
interface UploadSuccessRes {
  code: number;
  data: {
    file_url: string;
    original_file_name: string;
  };
  msg: string;
}
interface Form {
  file_name: string;
  project: string;
  lib_id?: number;
}
</script>
<script setup lang="ts">
// base
const { t: $t } = useI18n();
const props = withDefaults(defineProps<EditTagLibProps>(), {
  visible: false,
});
const emit = defineEmits<{
  (event: 'update:visible', value: boolean): void;
  (event: 'success'): void;
}>();
const value = computed({
  get: () => props.visible,
  set: (val: boolean) => emit('update:visible', val),
});
const close = () => {
  value.value = false;
};
// business
const gameList = computed(() => useEnumStore().gameList);
const enumList = computed(() => useEnumStore().enumList);
const token = computed(() => useUserInfoStore().userTocken);
const loading = ref(false);
const formRef = ref<FormInstance>();
const form = reactive<Form>({
  file_name: '',
  project: '',
});
const rules = reactive<FormRules>({
  file_name: [{ required: true, message: $t('place_input'), trigger: 'change' }],
  project: [{ required: true, message: $t('place_select'), trigger: 'change' }],
});
const submit = () => {
  formRef.value!.validate(async valid => {
    if (!valid || loading.value) return;
    loading.value = true;
    try {
      await gameGoodsConfigUpload(form);
      ElMessage.success($t('text_success'));
      emit('success');
      close();
    } catch (error) {
      console.log(error);
    } finally {
      loading.value = false;
    }
  });
};

// upload
const upload = ref<UploadInstance>();
const fileList = ref<UploadFile[]>([]);
const handleBeforeUpload = (file: File) => {
  const fileType = file.name.substring(file.name.lastIndexOf('.') + 1);
  if (fileType !== 'xls' && fileType !== 'xlsx') {
    ElMessage.error('请上传excel文件！');
    return false;
  }
};
const handleSuccess = ({ code, data, msg }: UploadSuccessRes) => {
  if (code !== 0) {
    ElMessage.error(msg);
    upload.value!.clearFiles();
    return;
  }
  form.file_name = data.file_url;
  fileList.value.push({
    name: data.original_file_name,
    url: data.file_url,
    status: 'success',
    uid: genFileId(),
  });
  ElMessage.success('上传成功！');
  formRef.value!.validateField('file_name');
};
const handleFailed = () => {
  ElMessage.error('上传失败！');
};
const handleRemove = (file: UploadFile) => {
  const index = fileList.value.indexOf(file);
  fileList.value.splice(index, 1);
  form.file_name = '';
};
const handlePreview = (file: UploadFile) => {
  const a = document.createElement('a');
  const event = new MouseEvent('click');
  a.download = file.name;
  a.href = file.url as string;
  a.dispatchEvent(event);
};
</script>

<style scoped>
.upload_box {
  width: 100%;
}
</style>
