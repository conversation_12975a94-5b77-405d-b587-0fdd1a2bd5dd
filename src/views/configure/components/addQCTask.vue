<template>
  <el-dialog
    v-model="value"
    width="600px"
    :title="$t('text_new_add')"
    :destroy-on-close="true"
    :before-close="close"
    :close-on-click-modal="false"
  >
    <el-form
      class="form"
      label-position="top"
      :model="form"
      :rules="rules"
      ref="formRef"
      v-loading="loading"
      size="small"
    >
      <!-- 任务名称 -->
      <el-form-item :label="$t('text_QC_task_name')" prop="task_name">
        <el-input v-model="form.task_name" clearable :placeholder="$t('know_m_rich_placeholder')" />
      </el-form-item>
      <!-- 游戏 -->
      <el-form-item :label="$t('text_game')" prop="project">
        <el-select v-model="form.project" :placeholder="$t('place_select')" clearable filterable>
          <el-option
            v-for="(v, index) in gameList"
            :key="index"
            :label="v.app_name"
            :value="v.game_project"
          ></el-option>
        </el-select>
      </el-form-item>
      <!-- 关联打分表 -->
      <el-form-item :label="$t('text_ass_QC_form')" prop="tpl_id">
        <opsSelect v-model="form.tpl_id" :placeholder="$t('place_select')" clearable filterable>
          <el-option
            v-for="(v, index) in qcFormTplList"
            :key="index"
            :label="v.tpl_dsc"
            :value="v.tpl_id"
          ></el-option>
        </opsSelect>
      </el-form-item>
      <!-- 抽检库选择 -->
      <el-form-item :label="$t('text_sampling_library')" prop="task_group">
        <el-radio-group v-model="form.task_group">
          <el-radio-button
            v-for="(i, key) in enumList.ExamineTaskGroup"
            :key="key"
            :label="i.name"
            :value="i.value"
          />
        </el-radio-group>
      </el-form-item>
      <!-- 工单库 -->
      <div class="ticket-form" v-if="form.task_group === 1"></div>
      <!-- DC库 -->
      <div class="QC-form" v-if="form.task_group === 2">
        <!-- 回复时间 -->
        <el-form-item :label="$t('text_recovery_time')" prop="filter_dsc_replied_at">
          <el-date-picker
            v-model="form.filter_dsc_replied_at as any"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="datetimerange"
            :range-separator="$t('to')"
            :start-placeholder="$t('text_recovery_time')"
            :end-placeholder="$t('text_recovery_time')"
          >
          </el-date-picker>
        </el-form-item>
        <!-- 全检标准 -->
        <el-form-item
          :label="$t('text_all_inspection_standard')"
          prop="filter_dsc_all_total_pay_gte"
        >
          <span style="margin: 0px 10px">{{ $t('text_charged_amount') }}</span>
          <el-input-number
            v-model="form.filter_dsc_all_total_pay_gte"
            :min="1"
            :max="1000000"
            :step="1"
            step-strictly
          />
          <span style="margin: 0px 25px 0px 5px">$</span>
          <el-button type="primary" :disabled="!form.project" @click="getDcNum">{{
            $t('text_num_search')
          }}</el-button>
          <span style="margin: 0px 10px">{{ $t('text_result_total', { total: dcNum }) }}</span>
        </el-form-item>
        <!-- 抽检范围 -->
        <el-form-item :label="$t('text_sampling_scope')" prop="filter_dsc_other_all">
          <el-radio-group v-model="form.filter_dsc_other_all">
            <el-radio-button :label="$t('text_all')" :value="true" />
            <el-radio-button :label="$t('text_part')" :value="false" />
          </el-radio-group>
        </el-form-item>
        <!-- 部分抽检 -->
        <div v-if="form.filter_dsc_other_all === false">
          <el-form-item>
            <el-radio-group v-model="form.task_rule">
              <el-radio :value="1">{{ $t('text_random_sampling') }}</el-radio>
              <el-radio :value="2">{{ $t('text_equal_sampling') }}</el-radio>
            </el-radio-group>
          </el-form-item>
          <!-- 标签部分暂缓开发 -->
          <!-- VIP专员 -->
          <el-form-item>
            <el-select
              v-model="form.filter_dsc_other_maintainer"
              multiple
              :reserve-keyword="false"
              :placeholder="$t('text_please_select_user')"
              filterable
              clearable
            >
              <el-option
                v-for="(v, index) in csList"
                :key="index"
                :label="v.account"
                :value="v.account"
              ></el-option>
            </el-select>
          </el-form-item>
          <!-- VIP状态 -->
          <el-form-item>
            <el-select
              v-model="form.filter_dsc_other_vip_state"
              multiple
              :placeholder="$t('text_vip_state')"
              clearable
            >
              <el-option
                v-for="(i, key) in enumList.PlayerVipState"
                :key="key"
                :label="i.name"
                :value="i.value"
              ></el-option>
            </el-select>
          </el-form-item>
          <!-- 查询数量 -->
          <el-form-item>
            <el-button type="primary" :disabled="!form.project" @click="getpartNum">{{
              $t('text_num_search')
            }}</el-button>
            <span style="margin: 0px 10px">{{ $t('text_result_total', { total: partNum }) }}</span>
          </el-form-item>
        </div>
        <!-- 抽检数量 -->
        <el-form-item :label="$t('text_random_check_quantity')" prop="filter_dsc_other_num">
          <el-input-number
            v-model="form.filter_dsc_other_num"
            :min="1"
            :max="1000"
            :step="1"
            step-strictly
          />
        </el-form-item>
        <!-- 分配人员 -->
        <el-form-item :label="$t('text_assigned_personnel')" prop="filter_dsc_assign_account">
          <el-select
            v-model="form.filter_dsc_assign_account"
            :disabled="!(serviceList && serviceList.length > 0)"
            filterable
            :reserve-keyword="false"
            multiple
            :placeholder="$t('place_select')"
            clearable
          >
            <el-option
              v-for="(v, index) in serviceList"
              :key="index"
              :label="v"
              :value="v"
            ></el-option>
          </el-select>
        </el-form-item>
      </div>
    </el-form>
    <template #footer>
      <el-button @click="close">{{ $t('btn_cancel') }}</el-button>
      <el-button type="primary" @click="submit">{{ $t('text_confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts">
import { useI18n } from 'vue-i18n';
import type { FormInstance, FormRules } from 'element-plus';
import { qcFormTpl, qcDcCount, qcPartCount, qcServiceList, qcTaskSave } from '@/api/QCtask';
import { getAcceptorList } from '@/api/assignConfig';
import { uploadImage } from '@/api/common';
import { useEnumStore } from '@/stores';
import domtoimage from 'dom-to-image';
export default defineComponent({
  name: 'AddQCTask',
  components: {
    ElMessage,
  },
});
interface AddQCTaskProps {
  visible: boolean;
}
interface Form {
  task_name: string;
  project: string;
  tpl_id?: number;
  task_group: number;
  filter_dsc_replied_at: string[];
  filter_dsc_all_total_pay_gte: number;
  filter_dsc_other_all: boolean;
  task_rule: number;
  filter_dsc_other_maintainer: string[];
  filter_dsc_other_vip_state: number[];
  filter_dsc_other_num: number;
  filter_dsc_assign_account: string[];
  link?: string;
}
</script>
<script setup lang="ts">
// base
const { t: $t } = useI18n();
const props = withDefaults(defineProps<AddQCTaskProps>(), {
  visible: false,
});
const emit = defineEmits<{
  (event: 'update:visible', value: boolean): void;
  (event: 'success'): void;
}>();
const value = computed({
  get: () => props.visible,
  set: (val: boolean) => emit('update:visible', val),
});
const close = () => {
  value.value = false;
};
// business
const csList = ref<Record<string, string>[]>([]);
const getCsList = async () => {
  const res = await getAcceptorList({});
  csList.value = res;
};
getCsList();

const qcFormTplList = ref();
qcFormTpl({}).then(
  (res: {
    list: Array<{
      tpl_id: number;
      tpl_desc: string;
    }>;
  }) => {
    qcFormTplList.value = res.list;
  }
);
const gameList = computed(() => useEnumStore().gameList);
const enumList = computed(() => useEnumStore().enumList);
const loading = ref(false);
const formRef = ref<FormInstance>();
const form = reactive<Form>({
  task_name: '',
  project: '',
  task_group: enumList.value.ExamineTaskGroup[1].value,
  filter_dsc_replied_at: [],
  filter_dsc_all_total_pay_gte: 1,
  filter_dsc_other_all: false,
  task_rule: 1,
  filter_dsc_other_maintainer: [],
  filter_dsc_other_vip_state: [],
  filter_dsc_other_num: 1,
  filter_dsc_assign_account: [],
  link: '',
});
const rules = reactive<FormRules>({
  task_name: [{ required: true, message: $t('place_input'), trigger: 'blur' }],
  project: [{ required: true, message: $t('place_select'), trigger: 'change' }],
  tpl_id: [{ required: true, message: $t('place_select'), trigger: 'change' }],
  task_group: [{ required: true, message: $t('place_select'), trigger: 'change' }],
  filter_dsc_replied_at: [{ required: true, message: $t('place_select'), trigger: 'change' }],
  filter_dsc_all_total_pay_gte: [{ required: true, message: $t('place_input'), trigger: 'change' }],
  filter_dsc_other_all: [{ required: true, message: $t('place_select'), trigger: 'change' }],
  filter_dsc_other_maintainer: [{ required: true, message: $t('place_select'), trigger: 'change' }],
  filter_dsc_other_vip_state: [{ required: true, message: $t('place_select'), trigger: 'change' }],
  filter_dsc_other_num: [{ required: true, message: $t('place_input'), trigger: 'change' }],
  filter_dsc_assign_account: [{ required: true, message: $t('place_select'), trigger: 'change' }],
});
const submit = () => {
  formRef.value!.validate(async valid => {
    if (!valid || loading.value) return;
    // 弹窗快照
    const dialogEl = document.querySelector('.el-dialog') as HTMLElement;
    if (!dialogEl) {
      ElMessage.error($t('screenshot failed'));
      return;
    }
    try {
      const options = {
        quality: 1,
        bgcolor: '#ffffff',
        height: dialogEl.scrollHeight,
        width: dialogEl.scrollWidth,
        style: {
          transform: 'none',
          margin: '0',
          padding: '20px',
        },
      };

      // 获取文件流
      const file = await domtoimage.toBlob(dialogEl, options);
      const formdata = new FormData();
      formdata.append('file', file);
      // 上传截图
      try {
        const res = await uploadImage(formdata as any);
        form.link = res.url;
      } catch (err) {
        ElMessage.error(err as string);
      }
    } catch (err) {
      console.error('截图失败:', err);
      ElMessage.error($t('screenshot failed'));
    }

    loading.value = true;
    try {
      await qcTaskSave(form);
      ElMessage.success($t('text_success'));
      emit('success');
      close();
    } catch (error) {
      console.log(error);
    } finally {
      loading.value = false;
    }
  });
};
// 获取全检任务数量
const dcNum = ref(0);
const getDcNum = () => {
  qcDcCount({
    replied_at: form.filter_dsc_replied_at,
    project: form.project,
    total_pay: form.filter_dsc_all_total_pay_gte,
  }).then((res: { num: number }) => {
    dcNum.value = res.num;
  });
};

// 部分-查询数量
const partNum = ref(0);
const getpartNum = () => {
  qcPartCount({
    replied_at: form.filter_dsc_replied_at,
    project: form.project,
    total_pay: form.filter_dsc_all_total_pay_gte,
    maintainer: form.filter_dsc_other_maintainer,
    vip_state: form.filter_dsc_other_vip_state,
    is_part: true,
  }).then((res: { num: number }) => {
    partNum.value = res.num;
  });
};

// 获取客服专员列表
const serviceList = ref();
const getServiceList = () => {
  qcServiceList({
    prd_code: 'ops-ticket-api',
    project: form.project,
  }).then((res: string[]) => {
    serviceList.value = res;
  });
};
// 监听form.project变化
watch(
  () => form.project,
  n => {
    dcNum.value = 0;
    partNum.value = 0;
    serviceList.value = [];
    form.filter_dsc_assign_account = [];
    if (n) {
      getServiceList();
    } else {
      serviceList.value = [];
    }
  }
);
// 监听form.filter_dsc_other_all变化
watch(
  () => form.filter_dsc_other_all,
  n => {
    if (n) {
      form.filter_dsc_other_maintainer = [];
      form.filter_dsc_other_vip_state = [];
    }
  }
);
</script>

<style lang="scss" scoped>
.form {
  &::v-deep(.el-form-item__label) {
    font-weight: 700;
  }
}
</style>
