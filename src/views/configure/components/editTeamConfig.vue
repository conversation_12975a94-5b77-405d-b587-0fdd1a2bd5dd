<template>
  <el-dialog
    v-model="value"
    width="600px"
    :title="Object.keys(props.editData).length > 0 ? $t('text_edit_config') : $t('text_add_config')"
    :destroy-on-close="true"
    :before-close="close"
    :close-on-click-modal="false"
  >
    <el-form
      class="form"
      label-position="top"
      size="small"
      :model="form"
      :rules="rules"
      ref="formRef"
      v-loading="loading"
    >
      <el-form-item :label="$t('text_team_name')" prop="team_name">
        <el-input v-model="form.team_name" clearable :placeholder="$t('place_input')" />
      </el-form-item>
      <el-form-item :label="$t('text_team_user')" prop="team_member">
        <el-select-v2
          v-model="form.team_member"
          filterable
          :reserve-keyword="false"
          :options="csList"
          :placeholder="$t('place_select')"
          multiple
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="close">{{ $t('btn_cancel') }}</el-button>
      <el-button type="primary" @click="submit">{{ $t('text_confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts">
import { useI18n } from 'vue-i18n';
import type { FormInstance, FormRules } from 'element-plus';
import { teamConfigSave, getAcceptorList, teamConfigEdit } from '@/api/assignConfig';
export default defineComponent({
  name: 'EditTeam',
  components: {
    ElMessage,
  },
});
interface EditTagLibProps {
  visible: boolean;
  editData?: Record<string, unknown>;
}
interface Form {
  team_id?: number;
  team_name: string;
  team_member: any[];
}
</script>
<script setup lang="ts">
// base
const { t: $t } = useI18n();
const props = withDefaults(defineProps<EditTagLibProps>(), {
  visible: false,
  editData: () => ({}),
});
const emit = defineEmits<{
  (event: 'update:visible', value: boolean): void;
  (event: 'success'): void;
}>();
const value = computed({
  get: () => props.visible,
  set: (val: boolean) => emit('update:visible', val),
});
const close = () => {
  value.value = false;
};
// business
const csList = ref<{ label: string; value: string }[]>([]);
const getCsList = async () => {
  csList.value = [];
  const res = await getAcceptorList({});
  res.forEach((v: any) => {
    csList.value.push({
      label: v.account,
      value: v.account,
    });
  });
};
getCsList();
const loading = ref(false);
const formRef = ref<FormInstance>();
const form = reactive<Form>({
  team_name: '',
  team_member: [],
});

const rules = reactive<FormRules>({
  team_name: [{ required: true, message: $t('place_input'), trigger: 'blur' }],
  // team_member: [{ required: true, message: $t('place_select'), trigger: 'change' }]
});

const submit = () => {
  formRef.value!.validate(async valid => {
    if (!valid || loading.value) return;
    loading.value = true;
    try {
      if (form.team_id) {
        await teamConfigEdit({
          team_id: form.team_id,
          team_name: form.team_name,
          team_member: form.team_member.join(','),
        });
      } else {
        await teamConfigSave({
          team_name: form.team_name,
          team_member: form.team_member.join(','),
        });
      }
      ElMessage.success($t('text_success'));
      emit('success');
      close();
    } catch (error) {
      console.log(error);
    } finally {
      loading.value = false;
    }
  });
};

// edit
onMounted(() => {
  if (Object.keys(props.editData).length > 0) {
    form.team_id = props.editData.team_id as number;
    form.team_name = props.editData.team_name as string;
    form.team_member = (props.editData.team_member as string).split(',');
  }
});
</script>

<style scoped></style>
