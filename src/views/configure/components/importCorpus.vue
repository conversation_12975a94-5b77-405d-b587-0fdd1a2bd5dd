<template>
  <el-dialog
    :title="$t('text_batch_import')"
    :model-value="visible"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="closeHandle"
    width="600px"
  >
    <div class="import-steps">
      <div class="step-item">
        <div class="step-num">1</div>
        <!-- 请按照模版格式准备需要导入的数据 -->
        <div class="step-text">{{ $t('text_prepare_data_according_to_template') }}</div>
      </div>
      <!-- 下载模板规范 -->
      <el-button type="primary" @click="downloadTemplate">
        {{ $t('text_import_template_download') }}
      </el-button>
    </div>
    <div class="import-steps">
      <div class="step-item">
        <div class="step-num">2</div>
        <!-- 请选择需要导入的文件 -->
        <div class="step-text">{{ $t('text_import_file_upload') }}</div>
      </div>
      <el-upload
        class="upload-demo"
        action="#"
        :auto-upload="false"
        :on-change="handleFileChange"
        :limit="1"
        :file-list="fileList"
        accept=".xlsx,.xls"
      >
        <template #trigger>
          <!-- 点击上传 -->
          <el-button type="primary">{{ $t('text_import_file_upload_btn') }}</el-button>
        </template>
        <template #tip>
          <div class="el-upload__tip">
            {{ $t('text_import_file_upload_tip') }}
          </div>
        </template>
      </el-upload>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="closeHandle">{{ $t('btn_cancel') }}</el-button>
        <el-button type="primary" @click="submitHandle" :loading="loading">{{
          $t('text_confirm')
        }}</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { useI18n } from 'vue-i18n';
// @ts-expect-error
import { importTicketCorpus } from '@/api/ticketCorpus';
import { ElMessage } from 'element-plus';

export default defineComponent({
  name: 'ImportCorpus',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
  },
  emits: ['update:visible', 'success'],
});
</script>

<script setup lang="ts">
import { ref, computed } from 'vue';
import type { FormInstance, FormRules, UploadFile } from 'element-plus';
// @ts-expect-error
import { useEnumStore, useUserInfoStore } from '@/stores';
// @ts-expect-error
import { downloadFile } from '@/api/common';

import config from '@/api/config';

const API = `${config.BASE_API}`;

const props = defineProps<{
  visible: boolean;
}>();

const emit = defineEmits(['update:visible', 'success']);
const { t: $t } = useI18n();
const gameList = computed(() => useEnumStore().gameList);
const langList = computed(() => useEnumStore().LangsList);
const token = computed(() => useUserInfoStore().userTocken);

const formRef = ref<FormInstance>();
const form = ref({
  file_name: '',
});

const fileList = ref<UploadFile[]>([]);

const rules = ref<FormRules>({
  file_name: [{ required: true, message: $t('text_please_select_file'), trigger: 'change' }],
});

const loading = ref(false);

const enumList = computed(() => useEnumStore().enumList);

// 当选择文件时触发
const handleFileChange = (file: UploadFile) => {
  fileList.value = [file];
  // 需要上传文件并获取URL
  if (file.raw) {
    uploadFile(file.raw);
  }
};

// 上传文件并获取URL
const uploadFile = async (file: File) => {
  try {
    loading.value = true;
    // 创建FormData对象
    const formData = new FormData();
    formData.append('file', file);

    // 调用上传文件的API，获取文件URL
    const res = await fetch(`/gateway/proxy/cs-api/all/v1/upload_excel`, {
      method: 'POST',
      body: formData,
      headers: {
        'admin-gateway-token': token.value,
      },
    });

    console.log('上传文件结果 res', res);

    const result = await res.json();

    console.log('上传文件结果 result', result);

    if (result.code === 0 && result.data) {
      form.value.file_name = result.data?.file_url || '';
      ElMessage.success($t('text_import_success'));
    } else {
      ElMessage.error(result.msg || $t('text_import_failed'));
    }
  } catch (error) {
    console.error('Upload error:', error);
    ElMessage.error(error.message || $t('text_import_failed'));
  } finally {
    loading.value = false;
  }
};

const closeHandle = () => {
  fileList.value = [];
  form.value = {
    file_name: '',
  };
  emit('update:visible', false);
};

// 下载导入模板
const downloadTemplate = () => {
  downloadFile({
    filename: $t('text_import_template_download') + '.xlsx',
    url: enumList.value.DownloadUrl.filter(
      (i: Record<string, string>) => i.name === 'TicketTrainDemo'
    )[0].value,
  });
};

const submitHandle = async () => {
  if (!form.value.file_name) {
    ElMessage.warning($t('text_please_upload_file_first'));
    return;
  }

  try {
    loading.value = true;

    await importTicketCorpus(form.value);
    ElMessage.success($t('text_import_success'));
    emit('success');
    closeHandle();
  } catch (error) {
    console.error(error);
    ElMessage.error($t('text_import_failed'));
  } finally {
    loading.value = false;
  }
};
</script>

<style scoped lang="scss">
.dialog-footer {
  display: flex;
  justify-content: flex-end;
}
.upload-demo {
  width: 100%;
}
.import-steps {
  margin-bottom: 20px;
  padding: 10px;
  button {
    align-items: center;
    margin-top: 20px;
  }
}
.step-item {
  display: flex;
  align-items: center;
}
.step-num {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background-color: #409eff;
  color: white;
  border-radius: 50%;
  margin-right: 10px;
  font-weight: bold;
}
.step-text {
  font-size: 14px;
}
</style>
