<template>
  <el-dialog
    v-model="value"
    width="600px"
    :title="Object.keys(props.editData).length > 0 ? $t('btn_edit') : $t('text_new_add')"
    :destroy-on-close="true"
    :before-close="close"
    :close-on-click-modal="false"
  >
    <el-form
      class="form"
      label-position="top"
      :model="form"
      :rules="rules"
      ref="formRef"
      v-loading="loading"
      size="small"
    >
      <el-form-item :label="$t('text_title')" prop="tpl_desc">
        <el-input v-model="form.tpl_desc" clearable :placeholder="$t('know_m_rich_placeholder')" />
      </el-form-item>
      <div
        v-for="(item, index) in form.module_detail"
        style="overflow: hidden; margin-bottom: 10px"
      >
        <el-card style="width: 94%; float: left" :key="index">
          <el-form-item
            :rules="{ required: true, message: $t('place_input'), trigger: 'blur' }"
            :label="$t('text_module') + (index + 1) + $t('text_title')"
            :prop="'module_detail.' + index + '.name'"
          >
            <el-input v-model="item.name" clearable :placeholder="$t('know_m_rich_placeholder')" />
          </el-form-item>
          <div
            v-for="(v, k) in item.field_detail"
            :key="k"
            style="overflow: hidden; margin-bottom: 10px"
          >
            <el-card style="width: 93%; float: left" class="no-paddbt">
              <!-- 考核项名称 -->
              <el-form-item
                :rules="{ required: true, message: $t('place_input'), trigger: 'blur' }"
                :label="$t('text_assessment') + (k + 1)"
                :prop="'module_detail.' + index + '.field_detail.' + k + '.field_name'"
              >
                <el-input
                  v-model="v.field_name"
                  clearable
                  :placeholder="$t('know_m_rich_placeholder')"
                />
              </el-form-item>
              <!-- 表单类型 -->
              <el-form-item
                :rules="{ required: true, message: $t('place_select'), trigger: 'change' }"
                :label="$t('text_form_type')"
              >
                <el-radio-group
                  v-model="v.field_type"
                  @change="newValue => fieldTypeChange(index, k, newValue)"
                >
                  <el-radio-button
                    v-for="(i, key) in enumList.ExamineFieldType"
                    :key="key"
                    :label="i.name"
                    :value="i.value"
                  />
                </el-radio-group>
              </el-form-item>
              <!-- options -->
              <div
                class="ops-box"
                v-if="v.field_type < 3"
                v-for="(opitem, opindex) in v.field_opts"
                :key="opindex"
              >
                <el-form-item
                  :prop="'module_detail.' + index + '.field_detail.' + k + '.field_opts.' + opindex"
                  :rules="{ required: true, message: $t('place_input'), trigger: 'blur' }"
                >
                  <el-input
                    v-model="v.field_opts[opindex]"
                    clearable
                    :placeholder="$t('text_option_value')"
                  />
                </el-form-item>
                <div class="bt_btn_box_line">
                  <el-button
                    type="primary"
                    text
                    class="bt_btn"
                    icon="CirclePlusFilled"
                    @click="addOps(index, k)"
                  />
                  <el-button
                    v-if="opindex > 0"
                    type="danger"
                    text
                    icon="DeleteFilled"
                    class="bt_btn"
                    @click="delOps(index, k, opindex)"
                  />
                </div>
              </div>
            </el-card>
            <div class="bt_btn_box">
              <el-button
                type="primary"
                text
                class="bt_btn"
                icon="CirclePlusFilled"
                @click="addAssessment(index)"
              />
              <el-button
                v-if="k > 0"
                type="danger"
                text
                icon="DeleteFilled"
                class="bt_btn"
                @click="delAssessment(index, k)"
              />
            </div>
          </div>
        </el-card>
        <div class="bt_btn_box">
          <el-button
            type="primary"
            text
            class="bt_btn"
            icon="CirclePlusFilled"
            @click="addModule"
          />
          <el-button
            v-if="index > 0"
            type="danger"
            text
            icon="DeleteFilled"
            class="bt_btn"
            @click="delModule(index)"
          />
        </div>
      </div>
    </el-form>
    <template #footer>
      <el-button @click="close">{{ $t('btn_cancel') }}</el-button>
      <el-button type="primary" @click="submit">{{ $t('text_confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts">
import { useI18n } from 'vue-i18n';
import type { FormInstance, FormRules } from 'element-plus';
import { useEnumStore } from '@/stores';
import { qcScoringFormSave, qcScoringFormDetail } from '@/api/QCscoringForm';
export default defineComponent({
  name: 'EditQCform',
  components: {
    ElMessage,
  },
});
interface EditQCformProps {
  visible: boolean;
  editData?: Record<string, unknown>;
}
interface Form {
  tpl_desc: string;
  id?: number;
  module_detail: Array<{
    name: string;
    field_detail: Array<{
      field_name: string;
      field_type: number;
      field_opts: string[];
    }>;
  }>;
}
</script>
<script setup lang="ts">
// base
const enumList = computed(() => useEnumStore().enumList);
const { t: $t } = useI18n();
const props = withDefaults(defineProps<EditQCformProps>(), {
  visible: false,
  editData: () => ({}),
});
const emit = defineEmits<{
  (event: 'update:visible', value: boolean): void;
  (event: 'success'): void;
}>();
const value = computed({
  get: () => props.visible,
  set: (val: boolean) => emit('update:visible', val),
});
const close = () => {
  value.value = false;
};

const loading = ref(false);
const formRef = ref<FormInstance>();
const form = reactive<Form>({
  tpl_desc: '',
  module_detail: [
    {
      name: '',
      field_detail: [
        { field_name: '', field_type: enumList.value.ExamineFieldType[0].value, field_opts: [''] },
      ],
    },
  ],
});
const rules = reactive<FormRules>({
  tpl_desc: [{ required: true, message: $t('place_input'), trigger: 'blur' }],
});

//模块新增删除
const addModule = () => {
  form.module_detail.push({
    name: '',
    field_detail: [
      { field_name: '', field_type: enumList.value.ExamineFieldType[0].value, field_opts: [''] },
    ],
  });
};
const delModule = (index: number) => {
  form.module_detail.splice(index, 1);
};
//考核项新增删除
const addAssessment = (index: number) => {
  form.module_detail[index].field_detail.push({
    field_name: '',
    field_type: enumList.value.ExamineFieldType[0].value,
    field_opts: [''],
  });
};
const delAssessment = (index: number, k: number) => {
  form.module_detail[index].field_detail.splice(k, 1);
};
// 选项新增删除
const addOps = (index: number, k: number) => {
  form.module_detail[index].field_detail[k].field_opts.push('');
};
const delOps = (index: number, k: number, opindex: number) => {
  form.module_detail[index].field_detail[k].field_opts.splice(opindex, 1);
};

// 表单类型改变
const fieldTypeChange = (index: number, k: number, newValue: number) => {
  if (newValue < 3) {
    form.module_detail[index].field_detail[k].field_opts = [''];
  } else {
    form.module_detail[index].field_detail[k].field_opts = [];
  }
};

// 提交
const submit = () => {
  console.log(form);
  formRef.value!.validate(async valid => {
    if (!valid || loading.value) return;
    loading.value = true;
    try {
      await qcScoringFormSave(form);
      ElMessage.success($t('text_success'));
      emit('success');
      close();
    } catch (error) {
      console.log(error);
    } finally {
      loading.value = false;
    }
  });
};

// edit
onMounted(() => {
  if (Object.keys(props.editData).length > 0) {
    loading.value = true;
    form.id = props.editData.id as number;
    form.tpl_desc = props.editData.tpl_desc as string;
    qcScoringFormDetail({ tpl_id: props.editData.id })
      .then((res: Form) => {
        form.module_detail = res.module_detail;
      })
      .finally(() => {
        loading.value = false;
      });
  }
});
</script>

<style lang="scss" scoped>
.form {
  .bt_btn_box {
    margin-left: 10px;
    float: left;
    margin-top: 10px;
    width: 24px;
    .bt_btn {
      float: left;
      margin: 10px auto;
      font-size: 25px;
      width: 22px;
      &:hover {
        background: none;
      }
    }
  }
}
.upload_box {
  width: 100%;
}
.no-paddbt {
  & ::v-deep(.el-card__body) {
    padding-bottom: 0;
  }
}
.ops-box {
  overflow: hidden;
  .el-form-item {
    width: 85%;
    display: inline-block;
  }
  .bt_btn_box_line {
    display: inline-block;
    margin-left: 10px;
    .bt_btn {
      font-size: 20px;
      padding: 0;
      &:hover {
        background: none;
      }
    }
  }
}
</style>
