<template>
  <el-dialog
    v-model="value"
    width="600px"
    :title="
      Object.keys(props.editData).length > 0 ? $t('text_edit_template') : $t('text_add_template')
    "
    :destroy-on-close="true"
    :before-close="close"
    :close-on-click-modal="false"
  >
    <el-form
      class="form"
      label-position="top"
      :model="form"
      size="small"
      :rules="rules"
      ref="formRef"
      v-loading="loading"
    >
      <!-- 游戏 -->
      <el-form-item :label="$t('text_game')" prop="game_project">
        <el-select
          v-model="form.game_project"
          :placeholder="$t('place_select')"
          clearable
          filterable
          @change="handleGame"
        >
          <el-option
            v-for="(v, index) in gameList"
            :key="index"
            :label="v.app_name"
            :value="v.game_project"
          ></el-option>
        </el-select>
      </el-form-item>
      <!-- 玩家DC账号 -->
      <el-form-item :label="$t('text_player_dc_account')" v-if="props.editData.dsc_user_id">
        <el-select
          :disabled="Object.keys(props.editData).length > 0"
          v-model="form.nick_name"
          @change="handleDesIds"
          :placeholder="$t('place_select')"
          clearable
        >
          <el-option
            v-for="item in playerList"
            :key="item"
            :label="item.user_name"
            :value="item.user_name"
          ></el-option>
        </el-select>
      </el-form-item>
      <!-- 玩家Line账号 -->
      <el-form-item :label="$t('text_player_line_account')" v-else>
        <el-select
          :disabled="Object.keys(props.editData).length > 0"
          v-model="form.nick_name"
          @change="handleDesIds"
          :placeholder="$t('place_select')"
          clearable
        >
          <el-option
            v-for="item in playerList"
            :key="item"
            :label="item.user_name"
            :value="item.user_name"
          ></el-option>
        </el-select>
      </el-form-item>
      <!-- fpid -->
      <el-form-item :label="$t('text_correlation_fpid')" prop="fpid">
        <el-input v-model.trim="form.fpid" clearable :placeholder="$t('know_m_rich_placeholder')" />
      </el-form-item>
      <!-- uid -->
      <el-form-item :label="$t('text_relations_uid')" prop="uid">
        <el-input v-model.trim="form.uid" clearable :placeholder="$t('know_m_rich_placeholder')" />
      </el-form-item>
      <!-- 服务器 -->
      <!-- <el-form-item :label="$t('text_relations_sid')" prop="sid">
        <el-input v-model="form.sid" clearable :placeholder="$t('know_m_rich_placeholder')" />
      </el-form-item> -->
      <!-- 关联专员 -->
      <el-form-item :label="$t('text_correlation_commissioner')" prop="maintainer">
        <el-select v-model="form.maintainer" :placeholder="$t('place_select')" clearable filterable>
          <el-option
            v-for="(v, index) in maintainerList"
            :key="index"
            :label="v.account"
            :value="v.account"
          ></el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="close">{{ $t('btn_cancel') }}</el-button>
      <el-button type="primary" @click="submit">{{ $t('text_confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts">
import { useI18n } from 'vue-i18n';
import type { FormInstance, FormRules } from 'element-plus';
import {
  saveRelation,
  editRelationLib,
  getUserLists,
  getUserPool,
  lineEditRelationLib,
} from '@/api/relationship';
import { useEnumStore } from '@/stores';
export default defineComponent({
  name: 'EditRelationShip',
  components: {
    ElMessage,
  },
});
interface EditTempLibProps {
  visible: boolean;
  editData?: Record<string, unknown>;
  getGame: string;
}
interface Form {
  nick_name: string;
  fpid: string;
  uid?: number | unknown;
  sid: string;
  maintainer: string;
  game_project: string;
  dsc_user_id: string;
  vip_state?: number;
  id?: number;
}
</script>
<script setup lang="ts">
// base
const { t: $t } = useI18n();
const props = withDefaults(defineProps<EditTempLibProps>(), {
  visible: false,
  editData: () => ({}),
  getGame: '',
});
const emit = defineEmits<{
  (event: 'update:visible', value: boolean): void;
  (event: 'success'): void;
}>();
const value = computed({
  get: () => props.visible,
  set: (val: boolean) => emit('update:visible', val),
});
const close = () => {
  value.value = false;
};
// business
const gameList = computed(() => useEnumStore().gameList);
const loading = ref(false);
const formRef = ref<FormInstance>();
const maintainerList = ref([]);
const playerList = ref([]);
const form = reactive<Form>({
  nick_name: '',
  fpid: '',
  uid: 0,
  sid: '',
  maintainer: '',
  game_project: '',
  dsc_user_id: '',
});
const rules = reactive<FormRules>({
  fpid: [{ required: true, message: $t('place_input'), trigger: 'blur' }],
  uid: [{ required: true, message: $t('place_input'), trigger: 'blur' }],
  sid: [{ required: true, message: $t('place_input'), trigger: 'blur' }],
  game_project: [{ required: true, message: $t('place_select'), trigger: 'change' }],
  maintainer: [{ required: true, message: $t('place_select'), trigger: 'change' }],
});
const submit = () => {
  formRef.value!.validate(async valid => {
    if (!valid || loading.value) return;
    loading.value = true;
    try {
      if (Object.keys(props.editData).length > 0) {
        await (props.editData.dsc_user_id
          ? editRelationLib({ ...form, uid: Number(form.uid) })
          : lineEditRelationLib({ ...form, uid: Number(form.uid), project: form.game_project }));
      } else {
        await saveRelation(form);
      }
      ElMessage.success($t('text_success'));
      emit('success');
      close();
    } catch (error) {
      console.log(error);
    } finally {
      loading.value = false;
    }
  });
};
const handleGame = (val: string) => {
  form.game_project = val;
  getDc();
};
// 获取玩家DC账号
const getDc = () => {
  getUserPool({ page: 1, page_size: 200, project: [form.game_project] }).then((res: any) => {
    playerList.value = res.data;
  });
};
const handleDesIds = (val: any) => {
  playerList.value.forEach((i: any) => {
    if (i.user_name === val) {
      form.dsc_user_id = i.dsc_user_id;
    }
  });
};
// 获取专员
const getMaintainer = () => {
  getUserLists({}).then((res: any) => {
    maintainerList.value = res;
  });
};
getMaintainer();
// edit
onMounted(() => {
  if (Object.keys(props.editData).length > 0) {
    form.id = props.editData.id as number;
    form.nick_name = props.editData.nick_name as string;
    form.fpid = props.editData.fpid as string;
    form.uid = (props.editData.uid as number) === 0 ? '' : props.editData.uid;
    form.sid = props.editData.sid as string;
    form.game_project = (props.editData.game_project || props.editData.project) as string;
    form.maintainer = props.editData.maintainer as string;
    form.vip_state = props.editData.vip_state as number;
  }
});
</script>

<style scoped>
.upload_box {
  width: 100%;
}
</style>
