<template>
  <el-dialog
    :title="editData ? $t('btn_edit') : $t('text_new_add')"
    :model-value="visible"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="closeHandle"
    width="600px"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
      size="small"
      v-loading="loading"
    >
      <!-- 语料内容 -->
      <el-form-item :label="`${$t('text_corpus_content')}：`" prop="question_content">
        <el-input
          v-model="form.question_content"
          type="textarea"
          :rows="6"
          :placeholder="$t('know_m_rich_placeholder') + $t('text_corpus_content')"
        ></el-input>
      </el-form-item>
      <!-- 关联游戏 -->
      <el-form-item :label="`${$t('text_linked_game')}：`" prop="project">
        <el-select
          v-model="form.project"
          :placeholder="$t('place_select')"
          clearable
          filterable
          style="width: 100%"
          :disabled="!!editData"
        >
          <el-option
            v-for="(v, index) in gameList"
            :key="index"
            :label="v.app_name"
            :value="v.game_project"
          ></el-option>
        </el-select>
      </el-form-item>
      <!-- 语种 -->
      <el-form-item :label="`${$t('text_lang')}：`" prop="lang">
        <el-select
          v-model="form.lang"
          :placeholder="$t('place_select') + $t('text_lang')"
          clearable
          style="width: 100%"
          :disabled="!!editData"
        >
          <el-option
            v-for="(v, index) in langList"
            :key="index"
            :label="v.name"
            :value="v.code"
          ></el-option>
        </el-select>
      </el-form-item>
      <!-- 问题分类 -->
      <el-form-item :label="`${$t('text_issue_category')}：`" prop="cat_id">
        <el-cascader
          v-model="form.cat_id"
          :options="category"
          :placeholder="$t('place_select') + $t('text_issue_category')"
          clearable
          :props="{
            checkStrictly: true,
            emitPath: false,
            value: 'id',
            expandTrigger: 'hover',
            lazy: false,
            leaf: 'leaf',
            multiple: false,
          }"
          style="width: 100%"
        >
          <template #default="{ data }">
            <span :class="{ active: data.isInter == true }">{{ data.label }}</span>
          </template>
        </el-cascader>
      </el-form-item>
      <!-- 答案 -->
      <el-form-item :label="`${$t('text_answer')}：`" prop="answer">
        <el-input
          v-model="form.answer"
          type="textarea"
          :rows="6"
          :placeholder="$t('know_m_rich_placeholder') + $t('text_answer')"
        ></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="closeHandle">{{ $t('btn_cancel') }}</el-button>
        <el-button type="primary" @click="submitHandle" :loading="loading">{{
          $t('text_confirm')
        }}</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { useI18n } from 'vue-i18n';
// @ts-expect-error
import { useEnumStore } from '@/stores';
// @ts-expect-error
import { saveTicketCorpus } from '@/api/ticketCorpus';
// @ts-expect-error
import { questionOptsAdmin } from '@/api/overview';
import { ElMessage } from 'element-plus';

export default defineComponent({
  name: 'AddCorpus',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    editData: {
      type: Object,
      default: null,
    },
  },
  emits: ['update:visible', 'success'],
});
</script>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import type { FormInstance, FormRules } from 'element-plus';

const props = defineProps<{
  visible: boolean;
  editData: any;
}>();

const emit = defineEmits(['update:visible', 'success']);
const { t: $t } = useI18n();
const gameList = computed(() => useEnumStore().gameList);
const langList = computed(() => useEnumStore().LangsList);

const formRef = ref<FormInstance>();
const form = ref({
  question_id: '', // 编辑时使用
  project: '',
  lang: '',
  cat_id: null as number | string | null, // 可以是数字或字符串ID
  question_content: '',
  answer: '',
});

const rules = ref<FormRules>({
  project: [
    { required: true, message: $t('text_please_select') + $t('text_game'), trigger: 'change' },
  ],
  lang: [
    { required: true, message: $t('text_please_select') + $t('text_lang'), trigger: 'change' },
  ],
  cat_id: [
    {
      required: true,
      message: $t('text_please_select') + $t('text_problem_category'),
      trigger: 'change',
      validator: (rule, value, callback) => {
        if (!value) {
          callback(new Error($t('text_please_select') + $t('text_problem_category')));
          return;
        }

        if (!validateCatId(value, category.value)) {
          callback(new Error($t('text_invalid_category')));
          return;
        }

        callback();
      },
    },
  ],
  question_content: [
    {
      required: true,
      message: $t('know_m_rich_placeholder') + $t('text_corpus_content'),
      trigger: 'blur',
    },
  ],
  answer: [
    { required: true, message: $t('know_m_rich_placeholder') + $t('text_answer'), trigger: 'blur' },
  ],
});

const loading = ref(false);

// 问题类型级联数据
const category = ref([]);

// 获取问题类型数据
const getCategoryData = async () => {
  // console.log('getCategoryData form.value.project', form.value.project)
  if (!form.value.project) {
    category.value = [];
    return Promise.resolve([]);
  }

  loading.value = true;
  try {
    const res = await questionOptsAdmin({ project: form.value.project, scope: 0 });
    category.value = res || [];
    console.log('category', category.value);
    return Promise.resolve(category.value);
  } catch (error) {
    console.error('获取问题类型失败', error);
    ElMessage.error($t('text_get_category_failed'));
    category.value = [];
    return Promise.reject(error);
  } finally {
    loading.value = false;
  }
};

// 监听游戏选择变化，获取问题类型
watch(
  () => form.value.project,
  val => {
    console.log('project val', val);
    // 不重置问题分类选择，只在分类数据加载后再决定是否保留
    const currentCatId = form.value.cat_id;
    if (val.length > 0) {
      getCategoryData().then(() => {
        // 在数据加载完成后，检查当前cat_id是否在新的分类数据中存在
        if (currentCatId && !validateCatId(currentCatId, category.value)) {
          // 如果在新的分类数据中找不到对应的cat_id，则重置
          form.value.cat_id = null;
        }
      });
    } else {
      category.value = [];
      form.value.cat_id = null;
    }
  }
);

// 编辑数据变化监听
watch(
  () => props.editData,
  newVal => {
    if (newVal) {
      console.log('editData', newVal);
      // 先设置表单的基本信息
      form.value = {
        question_id: newVal.question_id || '',
        project: newVal.project || '',
        lang: newVal.lang || '',
        cat_id: newVal.cat_id || null, // 保留原始cat_id
        question_content: newVal.question_content || '',
        answer: newVal.answer_rich_text || '',
      };

      // 立即获取分类数据，确保在编辑模式下能加载正确的分类选项
      if (form.value.project) {
        getCategoryData();
      }
    } else {
      form.value = {
        question_id: '',
        project: '',
        lang: '',
        cat_id: null,
        question_content: '',
        answer: '',
      };
      category.value = [];
    }
  },
  { immediate: true }
);

const closeHandle = () => {
  emit('update:visible', false);
};

// 检查cat_id是否在分类数据中存在
const validateCatId = (catId, categories) => {
  if (!catId || !categories || categories.length === 0) return false;

  const findId = (cats, id) => {
    for (const cat of cats) {
      if (cat.id === id) return true;
      if (cat.children && cat.children.length) {
        if (findId(cat.children, id)) return true;
      }
    }
    return false;
  };

  return findId(categories, catId);
};

const submitHandle = async () => {
  if (!formRef.value) return;

  // 验证cat_id是否有效
  if (form.value.cat_id && !validateCatId(form.value.cat_id, category.value)) {
    ElMessage.warning($t('text_invalid_category'));
    return;
  }

  await formRef.value.validate(async valid => {
    if (valid) {
      try {
        loading.value = true;

        const params = {
          ...form.value,
          // 确保cat_id字段是有效值
          cat_id: form.value.cat_id != null ? form.value.cat_id : null,
        };

        await saveTicketCorpus(params);
        ElMessage.success($t('text_operate_success'));
        emit('success');
        closeHandle();
      } catch (error) {
        console.error(error);
        ElMessage.error(error.message);
      } finally {
        loading.value = false;
      }
    }
  });
};
</script>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
}
</style>
