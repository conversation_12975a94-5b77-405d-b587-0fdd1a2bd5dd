<template>
  <div class="page-view-wapper">
    <div class="search-box">
      <el-form :inline="true" :model="searchForm" size="small">
        <el-form-item :label="`${$t('text_task_config')}：`">
          <el-input v-model="searchForm.task_name" :placeholder="$t('know_m_rich_placeholder')"
            clearable />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" plain @click="searchHandle">{{ $t('btn_search') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button v-has="'configure:qctask:add'" size="small" type="primary" icon="Plus" plain @click="addTagLibHandle">{{ $t('text_add_config')
            }}</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="page-content-box">
      <ops-table ref="_table" :data-api="qcTaskList" :params="params">
        <el-table-column v-for="(item, index) in columns" :key="item.prop + index" :prop="item.prop" :label="item.label"
          :width="item.width ? item.width : 'auto'" :align="item.align ? item.align : 'center'">
          <template #default="scope">
            <template v-if="item.prop === 'status'">
              {{ enumList.ExamineTaskState.filter((i: Record<string, string>) => i.value === scope.row[item.prop])[0]?.name || '--' }}
            </template>
            <template v-else>
              {{ scope.row[item.prop] || '--' }}
            </template>
          </template>
        </el-table-column>
        <el-table-column fixed="right" :label="$t('btn_op')" width="100" align="center">
          <template #default="scope">
            <el-button link type="primary" :disabled="!scope.row.link" @click="detailHandle(scope.row.link)">{{ $t('text_details') }}</el-button>
          </template>
        </el-table-column>
      </ops-table>
    </div>
    <!-- 由于需求要求:两处查询数量和select配置的人员和新增时保持一致；后端通过接口返回详情内容,实现成本大(例:数量变了&人员离职);
     经需求评审时对齐解放方案：因为页面不允许编辑 只读，所以前端以快照的形式实现 -->
    <el-dialog v-model="detailDialog" width="600px" :title="$t('text_details')"
      :destroy-on-close="true" :before-close="close" :close-on-click-modal="false">
      <el-image :preview-teleported="true" hide-on-click-modal :src="detailImage" class="attach-img" />
    </el-dialog>
    <addQCTask v-if="tagEditVisible" v-model:visible="tagEditVisible" @success="searchHandle" />
  </div>
</template>

<script lang="ts">
import { qcTaskList } from '@/api/QCtask'
import { useEnumStore } from '@/stores'
import { useI18n } from 'vue-i18n'
import addQCTask from './components/addQCTask.vue'
export default defineComponent({
  name: 'QCtask',
  components: {
    addQCTask,

  }
})
</script>
<script setup lang="ts">
const enumList = computed(() => useEnumStore().enumList)
const { t: $t } = useI18n()
const _table = ref()
const searchForm = ref({
  task_name: ''
})
const params = computed(() => {
  return {
    ...searchForm.value
  }
})
const columns = computed((): Column[] => {
  return [
    { prop: 'id', label: $t('text_QC_task_id'), width: '120'},
    { prop: 'task_name', label: $t('text_QC_task_name') },
    { prop: 'operator', label: $t('text_operator')},
    { prop: 'updated_at', label: $t('text_created_at') },
    { prop: 'status', label: $t('text_status'), width: '100'}
  ]
})
const searchHandle = () => {
  _table.value.getData()
}

const tagEditVisible = ref(false)
const tagLibData = ref({})
const addTagLibHandle = () => {
  tagLibData.value = {}
  tagEditVisible.value = true
}

// 详情弹窗
const detailDialog = ref<boolean>(false)
const detailImage = ref<string>('')
const detailHandle = (link: string) => {
  detailImage.value = link
  detailDialog.value = true
}
</script>