<template>
  <div class="page-view-wapper">
    <div class="search-box">
      <el-form :inline="true" :model="searchForm" size="small">
        <!-- 游戏 -->
        <el-form-item :label="`${$t('text_game')}：`">
          <el-select
            v-model="searchForm.project"
            :placeholder="$t('place_select')"
            clearable
            filterable
            multiple
            style="width: 180px"
          >
            <el-option
              v-for="(v, index) in gameList"
              :key="index"
              :label="v.app_name"
              :value="v.game_project"
            ></el-option>
          </el-select>
        </el-form-item>
        <!-- 问题分类 -->
        <el-form-item :label="`${$t('text_issue_category')}：`" prop="cat_id">
          <el-cascader
            v-model="searchForm.cat_id"
            :options="category"
            collapse-tags
            :placeholder="$t('text_qtype')"
            :props="{ multiple: true, emitPath: false, value: 'id' }"
            clearable
            :disabled="searchForm.project.length !== 1"
          >
            <template #default="{ data }">
              <span :class="{ active: data.isInter == true }">{{ data.label }}</span>
            </template>
          </el-cascader>
        </el-form-item>
        <!-- 语料ID -->
        <el-form-item :label="`${$t('text_corpus_id')}：`">
          <el-input
            v-model="searchForm.question_id"
            controls="false"
            controls-position="right"
            :placeholder="$t('know_m_rich_placeholder') + $t('text_corpus_id')"
            style="width: 180px"
            clearable
          ></el-input>
        </el-form-item>
        <!-- 语料内容 -->
        <el-form-item :label="`${$t('text_corpus_content')}：`">
          <el-input
            v-model="searchForm.question_content"
            :placeholder="$t('know_m_rich_placeholder') + $t('text_corpus_content')"
            style="width: 180px"
            clearable
          ></el-input>
        </el-form-item>
        <!-- 语种 -->
        <el-form-item :label="`${$t('text_lang')}：`" prop="lang">
          <el-select
            v-model="searchForm.lang"
            :placeholder="$t('text_lang')"
            style="width: 180px"
            multiple
            collapse-tags
            collapse-tags-tooltip
            clearable
          >
            <template #header>
              <el-checkbox
                v-model="checkLang"
                :indeterminate="indeterminateLang"
                @change="langCheckAll"
                >{{ $t('text_select_all') }}</el-checkbox
              >
            </template>
            <el-option
              v-for="(v, index) in langList"
              :key="index"
              :label="v.name"
              :value="v.code"
            ></el-option>
          </el-select>
        </el-form-item>
        <!-- 更新时间 -->
        <el-form-item :label="`${$t('text_update_time')}：`">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="-"
            :start-placeholder="$t('text_start_time')"
            :end-placeholder="$t('text_over_time')"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            style="width: 240px"
            @change="handleDateChange"
          />
        </el-form-item>
        <!-- 操作 -->
        <el-form-item>
          <el-button type="primary" icon="Search" plain @click="searchHandle">{{
            $t('btn_search')
          }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="default" icon="RefreshRight" plain @click="resetSearchHandle">{{
            $t('btn_reset')
          }}</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="button-group">
      <el-button size="small" type="primary" icon="Upload" plain @click="importCorpusHandle">{{
        $t('text_batch_upload')
      }}</el-button>
      <el-button size="small" type="primary" icon="Download" plain @click="downloadHandle">{{
        $t('btn_download')
      }}</el-button>
      <el-button size="small" type="success" icon="SetUp" plain @click="trainHandle">{{
        $t('btn_train')
      }}</el-button>
    </div>
    <div class="page-content-box">
      <ops-table ref="_table" :data-api="getTicketCorpusList" :params="params" v-loading="loading">
        <el-table-column
          v-for="(item, index) in columns"
          :key="item.prop + index"
          :prop="item.prop"
          :label="item.label"
          :width="item.width ? item.width : 'auto'"
          :align="item.align ? item.align : 'center'"
        >
          <!-- <template #default="scope">
            {{ scope.row[item.prop] || '--' }}
          </template> -->
          <template #default="scope">
            <template v-if="item.prop === 'project'">
              <span>{{ gameList[scope.row.project]?.app_name || '--' }}</span>
            </template>
            <template v-else-if="item.prop === 'lang'">
              <span>{{ langList.find(v => v.code === scope.row.lang)?.name || '--' }}</span>
            </template>
            <template v-else>
              {{ scope.row[item.prop] || '--' }}
            </template>
          </template>
        </el-table-column>
        <el-table-column :label="$t('btn_op')" align="center" width="200">
          <template #default="scope">
            <el-button size="small" type="primary" plain @click="editCorpusHandle(scope.row)">{{
              $t('btn_edit')
            }}</el-button>
            <el-popconfirm :title="$t('text_delete_sure')" @confirm="deleteCorpusHandle(scope.row)">
              <template #reference>
                <el-button size="small" type="danger" plain>{{ $t('text_delete') }}</el-button>
              </template>
            </el-popconfirm>
          </template>
        </el-table-column>
      </ops-table>
    </div>
    <add-corpus
      v-if="addCorpusVisible"
      v-model:visible="addCorpusVisible"
      :edit-data="editData"
      @success="searchHandle"
    />
    <import-corpus v-if="importVisible" v-model:visible="importVisible" @success="searchHandle" />
    <train-corpus v-if="trainVisible" v-model:visible="trainVisible" @success="searchHandle" />
  </div>
</template>

<script lang="ts">
// @ts-expect-error
import { questionOptsAdmin } from '@/api/overview';
import {
  getTicketCorpusList,
  deleteTicketCorpus,
  exportCorpus,
  trainCorpus,
} from '@/api/ticketCorpus';
import { useI18n } from 'vue-i18n';
// @ts-expect-error
import { useEnumStore } from '@/stores';
import { defineComponent } from 'vue';
import AddCorpus from './components/addCorpus.vue';
import ImportCorpus from './components/importCorpus.vue';
import TrainCorpus from './components/trainCorpus.vue';
import { ElMessage, ElMessageBox } from 'element-plus';

export default defineComponent({
  name: 'TicketCorpus',
  components: { AddCorpus, ImportCorpus, TrainCorpus },
});
</script>

<script setup lang="ts">
import { ref, computed, nextTick, watch } from 'vue';

const gameList = computed(() => useEnumStore().gameList);
const langList = computed(() => useEnumStore().LangsList);
const enumList = computed(() => useEnumStore().enumList);

const { t: $t } = useI18n();
const _table = ref();

// 问题类型级联数据
const category = ref([]);

// 获取问题类型数据
const getCategoryData = async () => {
  if (searchForm.value.project.length !== 1) {
    category.value = [];
    return;
  }

  try {
    // 这里需要替换为实际的API调用，参考overview.vue中的实现
    // 使用固定的分类数据作为示例
    // 实际项目中可能需要通过API获取问题分类，例如:
    const res = await questionOptsAdmin({ project: searchForm.value.project[0], scope: 0 });
    category.value = res || [];
  } catch (error) {
    console.error('获取问题类型失败', error);
    ElMessage.error($t('text_get_category_failed'));
  }
};

// 语言选项
const languageOptions = ref([
  { label: $t('text_simplified_chinese'), value: 'zh-CN' },
  { label: $t('text_traditional_chinese'), value: 'zh-TW' },
  { label: $t('text_english'), value: 'en' },
  { label: $t('text_japanese'), value: 'ja' },
  { label: $t('text_korean'), value: 'ko' },
]);

const searchForm = ref({
  project: [], // 游戏，多选
  cat_id: [], // 问题分类，多选
  question_id: null, // 语料ID
  question_content: '', // 语料内容
  lang: [], // 语种，多选
  update_date: [] as string[], // 更新时间范围
  page: 1,
  page_size: 10,
});

const loading = ref(false);

// 语种全选相关
const checkLang = ref(false);
const indeterminateLang = ref(false);

// 全选语种
const langCheckAll = (val: boolean) => {
  if (val) {
    searchForm.value.lang = langList.value.map(item => item.code);
  } else {
    searchForm.value.lang = [];
  }
  indeterminateLang.value = false;
};

// 监听游戏选择变化，获取问题类型
watch(
  () => searchForm.value.project,
  val => {
    console.log('project val', val);
    // 重置问题分类选择
    searchForm.value.cat_id = [];
    if (val.length === 1) {
      getCategoryData();
    } else {
      category.value = [];
    }
  }
);

// 监听语种选择变化
watch(
  () => searchForm.value.lang,
  val => {
    const langCount = langList.value.length;
    checkLang.value = val.length === langCount;
    indeterminateLang.value = val.length > 0 && val.length < langCount;
  },
  { deep: true }
);

const dateRange = ref<[string, string] | null>(null);

const handleDateChange = (val: [string, string] | null) => {
  if (val) {
    searchForm.value.update_date = [val[0], val[1]];
  } else {
    searchForm.value.update_date = [];
  }
};

const params = computed(() => {
  return {
    ...searchForm.value,
    question_id: Number(searchForm.value.question_id),
  };
});

const columns = computed((): any[] => {
  return [
    { prop: 'question_id', label: $t('text_corpus_id'), width: '100' },
    { prop: 'question_content', label: $t('text_corpus_content') },
    { prop: 'lang', label: $t('text_lang'), width: '100' },
    { prop: 'project', label: $t('text_game'), width: '120' },
    { prop: 'category', label: $t('text_issue_category'), width: '120' },
    { prop: 'update_date', label: $t('text_update_time'), width: '160' },
    { prop: 'operator', label: $t('text_operator'), width: '120' },
  ];
});

const searchHandle = () => {
  searchForm.value.page = 1;
  _table.value.getData();
};

const resetSearchHandle = () => {
  searchForm.value = {
    project: [],
    cat_id: [],
    question_id: null,
    question_content: '',
    lang: [],
    update_date: [],
    page: 1,
    page_size: 10,
  };
  dateRange.value = null;
  category.value = [];
  // 确保params计算属性更新
  nextTick(() => {
    searchHandle();
  });
};

// 添加语料相关
const addCorpusVisible = ref(false);
const editData = ref(null);

const addCorpusHandle = () => {
  editData.value = null;
  addCorpusVisible.value = true;
};

const editCorpusHandle = (row: any) => {
  editData.value = row;
  addCorpusVisible.value = true;
};

// 删除语料相关
const deleteCorpusHandle = async (row: any) => {
  loading.value = true;
  try {
    await deleteTicketCorpus({ question_id: Number(row.question_id) });
    ElMessage.success($t('text_delete_success'));
    searchHandle();
  } catch (error) {
    console.error(error);
  } finally {
    loading.value = false;
  }
};

// 导入语料相关
const importVisible = ref(false);
const importCorpusHandle = () => {
  importVisible.value = true;
};

// 导出语料相关
const downloadHandle = async () => {
  try {
    const params = {
      ...searchForm.value,
    };
    // 调用导出API，通常应该返回文件URL或文件流
    const response = await exportCorpus(params);
    // 处理导出结果，例如下载文件
    if (response && response.data) {
      const url = response.data.file_url;
      const link = document.createElement('a');
      link.href = url;
      link.download = 'corpus_export.xlsx';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      ElMessage.success($t('text_export_success'));
    }
  } catch (error) {
    console.error(error);
    ElMessage.error($t('text_export_failed'));
  }
};

// 训练相关
const trainVisible = ref(false);
const trainHandle = () => {
  trainVisible.value = true;
};
</script>

<style scoped>
.button-group {
  margin-bottom: 15px;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  padding: 0 10px;
}
</style>
