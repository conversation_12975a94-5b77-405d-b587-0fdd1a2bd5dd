<template>
  <div class="page-view-wapper">
    <div class="search-box">
      <el-form :inline="true" :model="searchForm" size="small">
        <el-form-item :label="`${$t('text_scoring_form_config')}：`">
          <el-input
            v-model="searchForm.tpl_desc"
            :placeholder="$t('know_m_rich_placeholder')"
            clearable
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" plain @click="searchHandle">{{
            $t('btn_search')
          }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button
            size="small"
            v-has="'configure:qcform:add'"
            type="primary"
            icon="Plus"
            plain
            @click="addTagLibHandle"
            >{{ $t('text_add_config') }}</el-button
          >
        </el-form-item>
      </el-form>
    </div>
    <div class="page-content-box">
      <ops-table ref="_table" :data-api="qcScoringFormList" :params="params">
        <el-table-column
          v-for="(item, index) in columns"
          :key="item.prop + index"
          :prop="item.prop"
          :label="item.label"
          :width="item.width ? item.width : 'auto'"
          :align="item.align ? item.align : 'center'"
        >
          <template #default="scope">
            <template v-if="item.prop === 'projects'">
              <span v-for="(v, index) in scope.row[item.prop]" :key="index"
                >{{ v }}{{ index === scope.row.projects.length - 1 ? '' : '、' }}</span
              >
            </template>
            <template v-else-if="item.prop === 'enable'">
              <el-switch
                v-has="'configure:qcform:status'"
                v-model="scope.row[item.prop]"
                :active-value="true"
                :inactive-value="false"
                active-color="#13ce66"
                inline-prompt
                inactive-color="#ff4949"
                :active-text="$t('text_enable')"
                :inactive-text="$t('text_disable')"
                @change="switchEnable($event as boolean, scope.row)"
              >
              </el-switch>
            </template>
            <template v-else>
              {{ scope.row[item.prop] || '--' }}
            </template>
          </template>
        </el-table-column>
        <el-table-column fixed="right" :label="$t('btn_op')" width="200" align="center">
          <template #default="scope">
            <el-button
              v-has="'configure:qcform:edit'"
              link
              type="primary"
              @click="editHandle(scope.row)"
              >{{ $t('btn_edit') }}</el-button
            >
            <el-popconfirm title="删除确认" @confirm="delHandle(scope.row)">
              <template #reference>
                <el-button link type="danger" v-has="'configure:qcform:del'">删除</el-button>
              </template>
            </el-popconfirm>
            <el-button
              v-has="'configure:qcform:copy'"
              link
              type="primary"
              @click="copyHandle(scope.row)"
              >{{ $t('text_copy') }}</el-button
            >
          </template>
        </el-table-column>
      </ops-table>
    </div>
    <editQCform
      v-if="formEditVisible"
      v-model:visible="formEditVisible"
      :edit-data="QCformData"
      @success="searchHandle"
    />
    <el-dialog
      v-model="copyVisible"
      width="600px"
      :title="$t('text_copy')"
      :destroy-on-close="true"
      :before-close="close"
      :close-on-click-modal="false"
    >
      <el-form
        class="form"
        label-position="top"
        :model="copyForm"
        :rules="rules"
        ref="copyFormRef"
        v-loading="loading"
        size="small"
      >
        <el-form-item :label="$t('text_ren')" prop="tpl_desc">
          <el-input
            v-model="copyForm.tpl_desc"
            clearable
            :placeholder="$t('know_m_rich_placeholder')"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="close">{{ $t('btn_cancel') }}</el-button>
        <el-button type="primary" @click="copySubmit">{{ $t('text_confirm') }}</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts">
import {
  qcScoringFormList,
  qcScoringFormEnable,
  qcScoringFormDelete,
  qcScoringFormCopy,
} from '@/api/QCscoringForm';
import { useI18n } from 'vue-i18n';
import editQCform from './components/editQCform.vue';
import type { FormInstance, FormRules } from 'element-plus';
export default defineComponent({
  name: 'QCscoringForm',
  components: {
    editQCform,
    ElMessage,
  },
});
</script>
<script setup lang="ts">
const { t: $t } = useI18n();
const _table = ref();
const searchForm = ref({
  tpl_desc: '',
});
const params = computed(() => {
  return {
    ...searchForm.value,
  };
});
const columns = computed((): Column[] => {
  return [
    { prop: 'id', label: $t('text_QC_form_id'), width: '120' },
    { prop: 'tpl_desc', label: $t('text_QC_form_name') },
    { prop: 'operator', label: $t('text_operator') },
    { prop: 'updated_at', label: $t('text_created_at') },
    { prop: 'enable', label: $t('text_status'), width: '100' },
  ];
});
const searchHandle = () => {
  _table.value.getData();
};

const formEditVisible = ref(false);
const QCformData = ref({});
const addTagLibHandle = () => {
  QCformData.value = {};
  formEditVisible.value = true;
};

const switchEnable = async (value: boolean, row: Record<string, unknown>) => {
  try {
    await qcScoringFormEnable({
      object_id: row.id,
      enable: value,
    });
    ElMessage.success($t('text_status_success'));
    searchHandle();
  } catch (error) {
    console.log(error);
    row.enable = !value;
  }
};
const editHandle = (row: Record<string, unknown>) => {
  QCformData.value = row;
  formEditVisible.value = true;
};
const delHandle = async (row: Record<string, unknown>) => {
  try {
    await qcScoringFormDelete({ tpl_id: row.id });
    ElMessage.success($t('text_del_success'));
    searchHandle();
  } catch (error) {
    console.log(error);
  }
};
const copyVisible = ref(false);
const copyForm = reactive({
  tpl_desc: '',
  from_tpl_id: 0,
});
const copyFormRef = ref<FormInstance>();
const rules = reactive<FormRules>({
  tpl_desc: [{ required: true, message: $t('place_input'), trigger: 'blur' }],
});
const close = () => {
  copyForm.tpl_desc = '';
  copyForm.from_tpl_id = 0;
  copyVisible.value = false;
};
const copyHandle = (row: Record<string, unknown>) => {
  copyForm.from_tpl_id = row.id as number;
  copyVisible.value = true;
};
const copySubmit = () => {
  copyFormRef.value!.validate(async valid => {
    if (!valid) return;
    try {
      await qcScoringFormCopy(copyForm);
      ElMessage.success($t('text_success'));
      searchHandle();
      close();
    } catch (error) {
      console.log(error);
    }
  });
};
</script>
