<template>
  <div class="page-view-wapper">
    <div class="search-box">
      <el-form :inline="true" :model="searchForm" size="small">
        <el-form-item :label="`${$t('text_template_name')}：`">
          <el-input
            prefix-icon="Memo"
            v-model="searchForm.tpl_name"
            :placeholder="$t('know_m_rich_placeholder')"
            clearable
          />
        </el-form-item>
        <el-form-item :label="`${$t('text_linked_game')}：`">
          <el-select
            v-model="searchForm.game_project"
            :placeholder="$t('place_select')"
            multiple
            collapse-tags
            collapse-tags-tooltip
            clearable
            style="width: 180px"
          >
            <el-option
              v-for="(v, index) in gameList"
              :key="index"
              :label="v.app_name"
              :value="v.game_project"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" plain @click="searchHandle">{{
            $t('btn_search')
          }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button size="small" type="primary" icon="Plus" plain @click="addTempLibHandle">{{
            $t('text_new_add_template')
          }}</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="page-content-box">
      <ops-table
        ref="_table"
        :data-api="tempLibList"
        :params="params"
        tooltip-effect="dark"
        class="custom-table"
      >
        <el-table-column
          v-for="(item, index) in columns"
          :key="item.prop + index"
          :prop="item.prop"
          :label="item.label"
          :width="item.width ? item.width : 'auto'"
          :align="item.align ? item.align : 'center'"
          :show-overflow-tooltip="item.showTooltip"
        >
          <template #default="scope">
            <template v-if="item.prop === 'game_project'">
              <span v-for="(v, index) in scope.row[item.prop]" :key="index"
                >{{ v }}{{ index === scope.row.game_project.length - 1 ? '' : '、' }}</span
              >
            </template>
            <template v-else-if="item.prop === 'enable'">
              <el-switch
                v-model="scope.row[item.prop]"
                :active-value="1"
                :inactive-value="0"
                active-color="#13ce66"
                inline-prompt
                inactive-color="#ff4949"
                :active-text="$t('text_enable')"
                :inactive-text="$t('text_disable')"
                @change="switchEnable($event, scope.row)"
              >
              </el-switch>
            </template>
            <template v-else>
              {{ scope.row[item.prop] || '--' }}
            </template>
          </template>
        </el-table-column>
        <el-table-column fixed="right" :label="$t('btn_op')" width="200" align="center">
          <template #default="scope">
            <el-button link type="primary" @click="editHandle(scope.row)">{{
              $t('btn_edit')
            }}</el-button>
            <el-popconfirm :title="$t('text_delete_sure')" @confirm="delHandle(scope.row)">
              <template #reference>
                <el-button link type="danger">{{ $t('text_delete') }}</el-button>
              </template>
            </el-popconfirm>
          </template>
        </el-table-column>
      </ops-table>
    </div>
    <!-- 表格图片预览 -->
    <div class="image_preview">
      <el-image-viewer
        hide-on-click-modal
        @close="
          () => {
            showViewer = false;
          }
        "
        v-if="showViewer"
        :url-list="previewList"
      />
    </div>
    <editTimeoutTemplate
      v-if="tempEditVisible"
      v-model:visible="tempEditVisible"
      :edit-data="tempLibData"
      @success="searchHandle"
    />
    <el-dialog
      v-model="dialogVisible"
      width="600px"
      :title="$t('text_template_content')"
      :destroy-on-close="true"
      :close-on-click-modal="false"
    >
      <div class="content-height" @click="getImg($event)">
        <div v-html="csContent"></div>
      </div>
      <template #footer>
        <el-button
          @click="
            () => {
              dialogVisible = false;
              outPreview;
            }
          "
          >{{ $t('btn_cancel') }}</el-button
        >
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts">
import { tempLibList, editEnableTempLib, delTempLib } from '@/api/timeoutTemplateConfig';
import { useI18n } from 'vue-i18n';
import editTimeoutTemplate from './components/editTimeoutTemplate.vue';
import { useEnumStore } from '@/stores';
export default defineComponent({
  name: 'TimeoutTemplateConfig',
  components: {
    editTimeoutTemplate,
    ElMessage,
  },
});
interface Column {
  prop: string;
  label: string;
  width?: string;
  showTooltip?: boolean;
}
</script>
<script setup lang="ts">
const { t: $t } = useI18n();
const gameList = computed(() => useEnumStore().gameList);
const _table = ref();
const dialogVisible = ref(false);
const previewTimer = ref<number | null>(null);
const csContent = ref('');
const searchForm = ref({
  tpl_name: '',
  game_project: [],
});
const params = computed(() => {
  return {
    ...searchForm.value,
  };
});
const columns = computed((): Column[] => {
  return [
    { prop: 'id', label: $t('text_template_id'), width: '120' },
    { prop: 'tpl_name', label: $t('text_template_name') },
    { prop: 'game_project', label: $t('text_linked_game') },
    { prop: 'operator', label: $t('text_operator') },
    { prop: 'update_time', label: $t('text_created_at') },
    { prop: 'enable', label: $t('text_status'), width: '100' },
  ];
});
const searchHandle = () => {
  _table.value.getData();
};

const tempEditVisible = ref(false);
const tempLibData = ref({});
const showViewer = ref<boolean>(false);
const previewList = ref<string[]>([]);
const addTempLibHandle = () => {
  tempLibData.value = {};
  tempEditVisible.value = true;
};

const switchEnable = (value: number, row: Record<string, unknown>) => {
  editEnableTempLib({
    object_id: row.id,
    enable: value,
  })
    .then(() => {
      ElMessage.success($t('text_status_success'));
    })
    .finally(() => {
      searchHandle();
    });
};
const editHandle = (row: Record<string, unknown>) => {
  tempLibData.value = row;
  tempEditVisible.value = true;
};
const delHandle = async (row: Record<string, unknown>) => {
  try {
    await delTempLib({ id: row.id });
    ElMessage.success($t('text_del_success'));
    searchHandle();
  } catch (error) {
    console.log(error);
  }
};
const getImg = ($event: MouseEvent) => {
  const target = $event.target as HTMLImageElement | null;
  // 确保事件目标本身是图片
  if (target && target instanceof HTMLImageElement && target.tagName === 'IMG' && target.src) {
    previewList.value = [target.src];
    showViewer.value = true;
  }
};
const outPreview = () => {
  if (previewTimer.value) {
    clearTimeout(previewTimer.value);
    previewTimer.value = null;
  }
};
</script>

<style lang="scss">
.custom-table {
  .el-popper {
    font-size: 14px;
    max-width: 20%;
  }
}
.el-dialog__body {
  .content-height {
    max-height: 450px;
    overflow-y: auto;
    .ql-align-right {
      img {
        width: 200px;
        height: 200px;
      }
    }
  }
}
</style>
