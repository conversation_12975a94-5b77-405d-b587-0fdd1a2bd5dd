<template>
  <div class="dc-bot-config">
    <el-form :model="form" :rules="rules" ref="formRef" class="dc-bot-config-form">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item prop="project" required label=" ">
            <el-select
              v-model="form.project"
              :placeholder="$t('text_game')"
              clearable
              filterable
              style="width: 100%"
            >
              <el-option
                v-for="(v, index) in gameList"
                :key="index"
                :label="v.app_name"
                :value="v.game_project"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item prop="dsc_name" required label=" ">
            <el-input v-model="form.dsc_name" :placeholder="$t('text_input_dc_account')" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item prop="bot_config.guild_desc" required label=" ">
            <el-input
              v-model="form.bot_config.guild_desc"
              :placeholder="$t('text_input_server_name')"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item prop="app_id" required label=" ">
            <el-input v-model="form.app_id" :placeholder="$t('text_input_application_id')" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item prop="bot_config.public_key" required label=" ">
            <el-input
              v-model="form.bot_config.public_key"
              :placeholder="$t('text_input_public_key')"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item prop="bot_config.bot_token" required label=" ">
            <el-input v-model="form.bot_config.bot_token" :placeholder="$t('text_input_token')" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item prop="bot_config.guild_id" required label=" ">
            <el-input
              v-model="form.bot_config.guild_id"
              :placeholder="$t('text_input_server_id')"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="24">
          <el-form-item prop="bot_config.welcome_message" required label=" ">
            <el-input
              v-model="form.bot_config.welcome_message"
              type="textarea"
              :rows="4"
              :placeholder="$t('text_input_welcome_msg')"
              style="padding-right: 20px"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item class="dc-bot-config-form-button">
        <el-button type="primary" @click="handleVerifyServer" :loading="verifying">
          {{ $t('text_verify_server') }}
        </el-button>
        <el-button type="primary" @click="handlePublish" :disabled="!serverVerified">
          {{ $t('text_publish') }}
        </el-button>
      </el-form-item>
    </el-form>

    <ops-table ref="_table" :data-api="getDCBotConfigList" :params="params">
      <el-table-column prop="project" :label="$t('text_game')" align="center">
        <template #default="{ row }">
          {{ Object.values(gameList).find(item => item.game_project === row.project)?.app_name }}
        </template>
      </el-table-column>
      <el-table-column prop="dsc_name" :label="$t('text_dc_account')" align="center" />
      <el-table-column
        prop="bot_config.guild_desc"
        :label="$t('text_server_name')"
        align="center"
      />
      <el-table-column prop="app_id" :label="$t('text_application_id')" align="center" />
      <el-table-column prop="bot_status" :label="$t('text_status')" align="center">
        <template #default="{ row }">
          <el-tag :type="getStatusType(row.bot_status)">{{ getStatusText(row.bot_status) }}</el-tag>
        </template>
      </el-table-column>
      <!-- welcome_message -->
      <el-table-column
        prop="bot_config.welcome_message"
        :label="$t('text_welcome_message')"
        align="center"
      >
        <template #default="{ row }">
          <div class="welcome-message-cell-box">
            <div class="welcome-message-cell">
              <el-tooltip
                class="welcome-message-cell-tooltip"
                :content="row.bot_config.welcome_message"
                placement="top"
                :popper-style="{
                  maxWidth: '50%',
                  maxHeight: '300px',
                  overflowY: 'auto',
                  whiteSpace: 'pre-wrap',
                  wordBreak: 'break-word',
                  lineHeight: '1.5',
                }"
              >
                <span>{{ row.bot_config.welcome_message || '-' }}</span>
              </el-tooltip>
            </div>
            <el-icon class="edit-icon" @click="handleEditWelcomeMessage(row)">
              <Edit />
            </el-icon>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="updated_by" :label="$t('text_operator')" align="center" />
      <el-table-column prop="updated_at" :label="$t('text_operation_time')" align="center" />
    </ops-table>

    <!-- 编辑欢迎消息弹窗 -->
    <el-dialog
      v-model="welcomeMessageDialogVisible"
      :title="$t('text_edit_welcome_message')"
      width="500px"
    >
      <el-form :model="welcomeMessageForm" ref="welcomeMessageFormRef">
        <el-form-item
          prop="welcome_message"
          :rules="[{ required: true, message: t('text_required_field'), trigger: 'blur' }]"
        >
          <div class="emoji-input-container">
            <el-input
              v-model="welcomeMessageForm.welcome_message"
              type="textarea"
              :rows="4"
              :placeholder="$t('text_input_welcome_msg')"
              :show-word-limit="true"
              :maxlength="3000"
              :input-style="{ fontFamily: 'system-ui, -apple-system, sans-serif' }"
            />
            <div class="emoji-picker">
              <el-popover placement="bottom" :width="300" trigger="click">
                <template #reference>
                  <el-button type="primary" link>
                    <el-icon><ChatRound /></el-icon>
                  </el-button>
                </template>
                <div class="emoji-list">
                  <span
                    v-for="emoji in commonEmojis"
                    :key="emoji"
                    class="emoji-item"
                    @click="insertEmoji(emoji)"
                  >
                    {{ emoji }}
                  </span>
                </div>
              </el-popover>
            </div>
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="welcomeMessageDialogVisible = false">{{
            $t('text_cancel')
          }}</el-button>
          <el-button
            type="primary"
            @click="handleSaveWelcomeMessage"
            :loading="savingWelcomeMessage"
          >
            {{ $t('text_save') }}
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue';
import { ElMessage } from 'element-plus';
import { useI18n } from 'vue-i18n';
import { Edit, ChatRound } from '@element-plus/icons-vue';
import {
  getDCBotConfigList,
  verifyDCBotServer,
  publishDCBotConfig,
  updateWelcomeMessage,
} from '@/api/dcBotConfig';
import { useEnumStore } from '@/stores/user';
import { useUserInfoStore } from '@/stores/user';

const { t } = useI18n();
const enumStore = useEnumStore();
const userStore = useUserInfoStore();
const loading = ref(false);
const verifying = ref(false);
const serverVerified = ref(false);
const formRef = ref();
const _table = ref();

const form = reactive({
  username: '',
  project: '',
  dsc_name: '',
  bot_desc: '',
  app_id: '',
  bot_config: {
    client_id: '',
    public_key: '',
    bot_token: '',
    guild_id: '',
    guild_desc: '',
    welcome_message: '',
  },
});

const params = computed(() => {
  return {};
});

const rules = {
  project: [{ required: true, message: t('text_required_field'), trigger: 'change' }],
  dsc_name: [{ required: true, message: t('text_required_field'), trigger: 'blur' }],
  app_id: [{ required: true, message: t('text_required_field'), trigger: 'blur' }],
  'bot_config.public_key': [{ required: true, message: t('text_required_field'), trigger: 'blur' }],
  'bot_config.bot_token': [{ required: true, message: t('text_required_field'), trigger: 'blur' }],
  'bot_config.guild_id': [{ required: true, message: t('text_required_field'), trigger: 'blur' }],
  'bot_config.guild_desc': [{ required: true, message: t('text_required_field'), trigger: 'blur' }],
  'bot_config.welcome_message': [
    { required: true, message: t('text_required_field'), trigger: 'blur' },
  ],
};

const tableData = ref([]);

// 状态类型映射
const statusTypes = {
  0: 'warning', // 处理中
  1: 'success', // 已发布
  2: 'danger', // 发布失败
};

// 状态文本映射
const statusTexts = {
  0: t('text_processing'),
  1: t('text_published'),
  2: t('text_publish_failed'),
};

const getStatusType = (status: number) => {
  return statusTypes[status] || 'info';
};

const getStatusText = (status: number) => {
  return statusTexts[status] || t('text_unknown');
};

const gameList = computed(() => useEnumStore().gameList);

console.log(gameList.value);

const handleVerifyServer = async () => {
  if (!formRef.value) return;
  await formRef.value.validate(async valid => {
    if (valid) {
      verifying.value = true;
      try {
        const submitData = {
          ...form,
          bot_desc: form.dsc_name,
          username: form.dsc_name,
        };
        const res = await verifyDCBotServer(submitData);
        console.log(res);
        if (res.data.code === 0) {
          ElMessage.success(t('text_server_verify_success'));
          serverVerified.value = true;
        } else {
          ElMessage.error(t('text_server_verify_failed'));
          serverVerified.value = false;
        }
      } catch (error) {
        console.error(error);
        ElMessage.error(t('text_server_verify_failed'));
        serverVerified.value = false;
      }
      verifying.value = false;
    }
  });
};

const handlePublish = async () => {
  if (!formRef.value) return;
  if (!serverVerified.value) {
    ElMessage.error(t('text_please_verify_server_first'));
    return;
  }
  await formRef.value.validate(async valid => {
    if (valid) {
      try {
        const submitData = {
          ...form,
          bot_config: {
            ...form.bot_config,
            client_id: form.app_id,
          },
          bot_desc: form.dsc_name,
          username: form.dsc_name,
        };
        const res = await publishDCBotConfig(submitData);
        if (res.data.code === 0) {
          ElMessage.success(t('text_publish_success'));
          _table.value.getData();
          formRef.value.resetFields();
        } else {
          ElMessage.error(res.data.msg);
        }
      } catch (error) {
        console.error(error);
      }
    } else {
      ElMessage.error(t('text_publish_failed_fill_all'));
    }
  });
};

// 欢迎消息编辑相关
const welcomeMessageDialogVisible = ref(false);
const welcomeMessageFormRef = ref();
const savingWelcomeMessage = ref(false);
const welcomeMessageForm = reactive({
  id: '',
  welcome_message: '',
});

const handleEditWelcomeMessage = row => {
  welcomeMessageForm.id = row.id;
  welcomeMessageForm.welcome_message = row.bot_config.welcome_message;
  welcomeMessageDialogVisible.value = true;
};

const handleSaveWelcomeMessage = async () => {
  if (!welcomeMessageFormRef.value) return;
  await welcomeMessageFormRef.value.validate(async valid => {
    if (valid) {
      savingWelcomeMessage.value = true;
      try {
        const res = await updateWelcomeMessage(welcomeMessageForm);
        if (res.data.code === 0) {
          ElMessage.success(t('text_save_success'));
          welcomeMessageDialogVisible.value = false;
          _table.value.getData();
        } else {
          ElMessage.error(res.data.msg || t('text_save_failed'));
        }
      } catch (error) {
        console.error(error);
        ElMessage.error(t('text_save_failed'));
      }
      savingWelcomeMessage.value = false;
    }
  });
};

// 常用表情列表
const commonEmojis = [
  '👋',
  '😊',
  '🎉',
  '✨',
  '🌟',
  '💫',
  '🚢',
  '⛴️',
  '🛳️',
  '🎮',
  '🎲',
  '🎯',
  '🏆',
  '🌈',
  '🎨',
  '🎭',
  '🎪',
  '🎠',
  '🎡',
  '🎢',
  '🌺',
  '🌸',
  '🌼',
  '🌻',
  '💝',
  '💖',
  '💗',
  '💓',
  '💞',
  '💕',
  '💘',
  '💟',
];

// 插入表情的方法
const insertEmoji = (emoji: string) => {
  const textarea = welcomeMessageFormRef.value.$el.querySelector('textarea');
  const start = textarea.selectionStart;
  const end = textarea.selectionEnd;
  const text = welcomeMessageForm.welcome_message;
  welcomeMessageForm.welcome_message = text.substring(0, start) + emoji + text.substring(end);
  // 设置光标位置
  setTimeout(() => {
    textarea.focus();
    textarea.setSelectionRange(start + emoji.length, start + emoji.length);
  }, 0);
};
</script>

<style lang="scss" scoped>
.dc-bot-config {
  padding: 20px;

  :deep(.el-tag) {
    width: 80px;
    text-align: center;
  }
}

.dc-bot-config-form {
  :deep(.el-form-item__label) {
    font-weight: normal;
    padding: 0;
  }
}

.dc-bot-config-form-button {
  :deep(.el-form-item__content) {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
  }
}

.welcome-message-cell-tooltip {
  width: 50%;
}

.welcome-message-cell-box {
  position: relative;
  padding-right: 20px;

  .edit-icon {
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
    z-index: 1;
  }
}
.welcome-message-cell {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  gap: 8px;
  > span {
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    padding: 0 8px;
  }
}

.edit-icon {
  cursor: pointer;
  color: #409eff;
}

.edit-icon:hover {
  color: #66b1ff;
}

.emoji-input-container {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;
  gap: 8px;
}

.emoji-picker {
  position: absolute;
  right: 8px;
  top: 8px;
}

.emoji-list {
  display: grid;
  grid-template-columns: repeat(8, 1fr);
  gap: 8px;
  padding: 8px;
}

.emoji-item {
  cursor: pointer;
  font-size: 20px;
  text-align: center;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s;

  &:hover {
    background-color: #f5f7fa;
  }
}
</style>
