<template>
  <div class="loginbox">
    <div class="lg-left">
      <div class="text-box">
        <p>FunPlus</p>
        <p><span style="color: #ff5a00">十万伏特系统 </span>管理后台</p>
        <span class="info">© FunPlus 2024 北京趣加科技有限公司</span>
      </div>
    </div>
    <div class="lg-right"></div>
    <div class="lg-content">
      <div class="animate-logo">
        <img src="/src/assets/img/fplogo.png" class="fun" />
        <img src="/src/assets/img/plus.png" class="plus" />
      </div>
      <el-button type="primary" @click="loginHandle" class="loginbtn">登录平台</el-button>
      <p class="tips">* 使用单点登录账户登录系统</p>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Login',
  methods: {
    loginHandle() {
      location.href = `/gateway/auth/login?referer=` + encodeURIComponent(`${location.origin}/`);
    },
  },
};
</script>
<style lang="scss" scoped>
@keyframes loadingLogo {
  0% {
    transform: rotate(45deg);
    top: -14px;
  }

  50% {
    top: -30px;
  }

  100% {
    top: -14px;
    transform: rotate(225deg);
  }
}
.animate-logo {
  margin-top: 40px;
  position: relative;
  transform: scale(0.8);
  .fun {
    display: block;
    width: 100%;
    height: 53.5px;
  }
  .plus {
    display: block;
    position: absolute;
    top: -14px;
    left: 37px;
    width: 35px;
    height: 35px;
    animation: loadingLogo 1.2s ease-in-out infinite;
  }
}
.loginbox {
  width: 100%;
  height: 100%;
  background: url('/src/assets/img/bg.jpg') no-repeat;
  background-size: cover;
  background-position: center;
  position: relative;
}
.lg-right {
  width: 25%;
  background-color: rgba(255, 90, 0, 0.8);
  background-image: url('/src/assets/img/login-right.png');
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center bottom;
}
.lg-left {
  width: 75%;
  position: relative;
}
.lg-left,
.lg-right {
  height: 100%;
  float: left;
}
.lg-content {
  position: absolute;
  right: 25%;
  top: 50%;
  width: 350px;
  height: 460px;
  box-shadow: 0px 4px 20px 0px rgba(0, 0, 0, 0.3);
  background: #ffffff;
  color: #444444;
  padding: 40px !important;
  z-index: 1;
  border-radius: 2px;
  box-sizing: border-box;
  margin-top: -230px;
  margin-right: -175px;

  .loginbtn {
    width: 100%;
    margin-top: 110px;
  }
  .tips {
    font-size: 12px;
    color: #ff9b65;
  }
}
.text-box {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 522px;
  height: 280px;
  margin-left: -410px;
  margin-top: -238px;
  .lit {
    font-size: 20px;
    margin: 104px 0px 0px 2px;
  }
  .info {
    font-size: 12px;
    color: #999;
    display: block;
    margin-top: 136px;
  }
}
.text-box p {
  font-weight: bold;
  font-size: 50px;
  line-height: 25px;
}
</style>
