<template>
  <div class="chat-box">
    <!-- 处理文字聊天内容 -->
    <div class="chat-content-box">
      <div class="mouse-wheel-pulldown">
        <div ref="refScroll" class="pulldown-wrapper">
          <div class="pulldown-content">
            <div class="pulldown-tips">
              <div v-show="beforePullDown">
                <span>{{ $t('text_loading_more') }}</span>
              </div>
              <div v-show="!beforePullDown">
                <div v-show="isPullingDown">
                  <span>{{ $t('text_loading_tip') }}</span>
                </div>
                <div v-show="!isPullingDown"><span>{{ $t('text_loading_success') }}</span></div>
              </div>
            </div>
            <div class="dc-dialog-scroll-wrapper" v-if="messageList.length > 0">
              <discordRenderer v-for="item in messageList"
                @reference-click="referenceClickHandle"
                :tooltip="false"
                class="pulldown-list-item"
                :key="item.msg_id"
                :dialog-data="item"
                :id="'Msg' + item.msg_id" />
            </div>
            <el-empty v-else :description="$t('info_no_data')" />
          </div>
        </div>
      </div>
    </div>
    <div class="chat-input-box">
      <el-button type="primary" v-has="'qc:discord:startqc'" v-if="props.partnerInfo.status !== 100"  @click="handleStartQC">{{ $t('text_start_QC') }}</el-button>
      <span class="QC-result-box" v-else>{{ $t('text_QC_result') }}：
        <span :style="{color: {1: '#4aa181', 2: '#F56C6C'}[props.partnerInfo.final_result as number]}">{{ enumList.ExamineFinalResult.filter((i: Record<string, number>) => i.value === props.partnerInfo.final_result)[0]?.name || '--' }}</span>
      </span>
      <!-- 未读消息按钮 -->
      <el-button v-if="showNewMsgBtn" type="primary" icon="ArrowDownBold" size="small" plain class="unread-btn"
        @click="scrollBottom">{{ $t('btn_not_read') }}</el-button>
      <!-- 快速触底按钮 -->
      <el-button v-if="showGoBottomBtn && !showNewMsgBtn" circle icon="ArrowDownBold" size="small" plain
        class="unread-btn" @click="scrollBottom" />
    </div>
  </div>
</template>

<script lang="ts">
import 'vue3-emoji-picker/css'
import { getMessageList, sendDcMessage, getNewMessageEvent, discordReplyMessage } from '@/api/chatting'
import { useDebounceFn } from '@vueuse/core'
import { useEnumStore } from '@/stores'

import { useI18n } from 'vue-i18n' // 国际化
import { spawn } from 'child_process'
interface PartnerInfo {
  bot_id: string // 机器人id
  msg_id: string
  dm_channel: string // 会话id
  dsc_user_id: string // 用户id
  global_name: string // 用户名
  guild_id: string // 公会id
  processor: string // 处理人
  project: string // 游戏
  total_pay: number // 总充值
  user_name: string // 用户名
  vip_level: number // vip等级
  uid?: number
  sid: string // 服务器
  pay_all: number // 玩家累计付费金额
  status: number // 质检状态
  final_result?: number // 质检结果
}
interface ChatProps {
  partnerInfo: PartnerInfo
}
interface Message {
  attach: any[] // 附件
  author: {
    avatar: string // 头像
    bot: boolean // 是否机器人
    global_name: string // 用户名
    id: string // 用户id
    username: string // 用户名
  }
  channel_id: string // 会话id
  content: string // 文字内容
  created_at: string // 创建时间
  embed: any[] // 嵌入
  from_user_id: string // 发送者id
  is_edited: boolean // 是否编辑过
  checked: boolean // 是否展示复选框
  msg_id: string // 消息id
  project: string // 游戏
  reactions: any[] // 表情
  referenced_msg?: Message // 引用消息
  referenced_msg_id: string // 引用消息id
  stickers: any[] // 贴纸
}
interface Reaction {
  name: string // 表情内容
  user_id: string // 用户id
  created_at: string // 创建时间
  msg_id: string // 消息id
}
interface DcEvent {
  event_type: number // 事件类型 0: 未知 1：新增消息 2：编辑消息 3：删除消息 4：新增反应 5：删除反应
  msg_detail: Message // 消息详情
  del_msg_ids: string[] // 删除的消息id
  add_reaction: Reaction[] // 新增的反应
  del_reaction: Reaction[] // 删除的反应
}
export default defineComponent({
  name: 'OpsQCDcChat',
  components: {
    ElMessage,
    ElNotification
  }
})
</script>
<script setup lang="ts">
import BScroll from '@better-scroll/core'
import PullDown from '@better-scroll/pull-down'
import MouseWheel from '@better-scroll/mouse-wheel'
const { t: $t } = useI18n()
const enumList = computed(() => useEnumStore().enumList)

// props
const props = withDefaults(defineProps<ChatProps>(), {
  partnerInfo: () => ({
    bot_id: '',
    msg_id: '',
    dm_channel: '',
    dsc_user_id: '',
    global_name: '',
    guild_id: '',
    processor: '',
    project: '',
    total_pay: 0,
    user_name: '',
    vip_level: 0,
    sid: '',
    pay_all: 0,
    status: 1,
  })
})

// 轮询接口心跳开关
const heartbeat = ref(true)

// BS相关
BScroll.use(PullDown)
BScroll.use(MouseWheel)
const refScroll = ref<HTMLElement | null>()
const observer = ref<ResizeObserver | null>()
const scroll = ref<BScroll | null>(null)
const beforePullDown = ref(true)
const isPullingDown = ref(false)
const autoScrollMark = ref(true)
const showGoBottomBtn = ref(false)
const activeTab = inject('activeTab') as Ref<number>

// 点击控制activeTab为3，开始质检
const handleStartQC = () => {
  activeTab.value = 3
}

// 监听activeTab变化，切换回聊天tab时拉取新消息，更新消息列表滚动到底部
watch(() => activeTab.value, (newVal) => {
  if (newVal === 1) {
    heartbeat.value = true
    getMessageEvent()
  } else if (newVal === 2) {
    heartbeat.value = false
  }
})

const initScroll = () => {
  scroll.value = new BScroll(refScroll.value!, {
    scrollY: true,
    bounceTime: 800,
    probeType: 3,
    mouseWheel: {
      speed: 30,
      invert: false,
    },
    disableMouse: true,
    preventDefault: false,
    pullDownRefresh: {
      threshold: 20,
      stop: 56
    }
  })
  // scroll.value.on('pullingDown', getHistoryMessage)
  let lastScrollY = 0
  scroll.value.on('scroll', (pos: any) => {
    // 下拉加在历史消息，不用BS自带的下拉刷新，对触摸板支持不好，有BUG，自己实现
    if (!(pos.y < 0) && !isPullingDown.value) {
      getHistoryMessage()
    }
    
    if (activeTab.value === 2) return // 避免tab切换时display为none时触发

    heartbeat.value = false
    if (pos.y > lastScrollY) {
      autoScrollMark.value = false
    }

    if (pos.y - scroll.value!.maxScrollY > 500) {
      showGoBottomBtn.value = true
    } else {
      showGoBottomBtn.value = false
    }
    
    lastScrollY = pos.y
  })

  scroll.value.on('scrollEnd', () => {
    if (activeTab.value === 2) return // 避免tab切换时display为none时触发

    heartbeat.value = true
    if (scroll.value!.y === scroll.value!.maxScrollY || scroll.value!.y < scroll.value!.maxScrollY) {
      autoScrollMark.value = true
      showNewMsgBtn.value = false
      showGoBottomBtn.value = false
    }
  })
}

// 点击引用消息，滚动到引用消息位置
const referenceClickHandle = (msgId: string) => {
  const target = document.getElementById('Msg' + msgId)

  if (target) {
    scroll.value!.scrollToElement(target, 500, false, false)
    setTimeout(() => {
      target.classList.add('pulse')
    }, 500)
    setTimeout(() => {
      target.classList.remove('pulse')
    }, 1500)
  } else {
    ElMessage.warning($t('text_msg_noload'))
  }
}

// 获取聊天记录
const messageList = ref<Message[]>([])
const getInitMessage = () => {
  getMessageList({ channel_id: props.partnerInfo.dm_channel }).then((res: any) => {
    messageList.value = res.dialog_list
    nextTick(() => {
      scroll.value!.refresh()
      // 滚动到最底部
      if (messageList.value.length < 1) return
      scroll.value!.scrollToElement('#Msg' + messageList.value[messageList.value.length - 1].msg_id, 0, false, false)
    })
  })
}

// 监听props.partnerInfo变化，拉取聊天记录
watch(() => props.partnerInfo, (newVal) => {
  getInitMessage()
}, {
  // 首次拉取聊天记录
  immediate: true
})

// 获取新消息、事件(用于轮询)
const showNewMsgBtn = ref(false)
const getMessageEvent = () => {
  getNewMessageEvent({
    channel_id: props.partnerInfo.dm_channel,
    after: messageList.value[messageList.value.length - 1].msg_id
  }).then((res: any) => {
    res.dialog_list.forEach((item: Message) => {
      if (!messageList.value.some(msg => msg.msg_id === item.msg_id)) {
        messageList.value.push(item)
        if (!autoScrollMark.value) {
          showNewMsgBtn.value = true
          ElNotification.success({
            title: $t('text_have_unread'),
            message: item.content.length > 20 ? item.content.slice(0, 20) + '...' : item.content,
            showClose: false,
          })
        }
      }
    })
    res.fresh_event.forEach((event: DcEvent) => {
      if (event.event_type === 2) {
        const index = messageList.value.findIndex(msg => msg.msg_id === event.msg_detail.msg_id)
        if (index !== -1) {
          messageList.value[index].is_edited = event.msg_detail.is_edited
          messageList.value[index].content = event.msg_detail.content
          messageList.value[index].attach = event.msg_detail.attach
          messageList.value[index].embed = event.msg_detail.embed
          messageList.value[index].stickers = event.msg_detail.stickers
        }
      } else if (event.event_type === 3) {
        messageList.value = messageList.value.filter(msg => !event.del_msg_ids.includes(msg.msg_id))
      } else if (event.event_type === 4) {
        const index = messageList.value.findIndex(msg => msg.msg_id === event.add_reaction[0].msg_id)
        if (index !== -1) {
          messageList.value[index].reactions = [...messageList.value[index].reactions, ...event.add_reaction]
        }
      } else if (event.event_type === 5) {
        const index = messageList.value.findIndex(msg => msg.msg_id === event.del_reaction[0].msg_id)
        if (index !== -1) {
          messageList.value[index].reactions = messageList.value[index].reactions.filter(reaction => {
            return !event.del_reaction.some(del => del.name === reaction.name)
          })
        }
      }
    })

    nextTick(() => {
      scroll.value!.refresh()
      // 滚动到最底部
      if (autoScrollMark.value) {
        scroll.value!.scrollToElement('#Msg' + messageList.value[messageList.value.length - 1].msg_id, 0, false, false)
      }
    })
  })
}
// 未读消息滚动底部功能
const scrollBottom = () => {
  scroll.value!.scrollTo(0, scroll.value!.maxScrollY, 500)
  setTimeout(() => {
    showNewMsgBtn.value = false
    showGoBottomBtn.value = false
  }, 500)
}

// 拉取历史消息
const getHistoryMessage = () => {
  beforePullDown.value = false
  isPullingDown.value = true
  heartbeat.value = false

  const lastMsgId = messageList.value[0].msg_id
  getMessageList({ 
    channel_id: props.partnerInfo.dm_channel,
    before: messageList.value[0].msg_id
  }).then((res: any) => {
    messageList.value.unshift(...res.dialog_list)
    nextTick(() => {
      scroll.value!.refresh()
      scroll.value!.scrollToElement('#Msg' + lastMsgId, 0, false, false)
    })
  }).finally(() => {
    isPullingDown.value = false
    heartbeat.value = true
    setTimeout(() => {
      scroll.value!.finishPullDown()
    }, 500)
    setTimeout(() => {
      beforePullDown.value = true
    }, 800)
  })
}

const isEmptyQuillContent = (html: string) => {
  const parser = new DOMParser()
  const doc = parser.parseFromString(html, "text/html")
  const text = doc.body.textContent || ""
  const images = doc.getElementsByTagName('img')
  let hasImageContent = false

  for (let i = 0; i < images.length; i++) {
    if (images[i].src.trim() !== '') {
      hasImageContent = true
      break
    }
  }

  return text === "" && !hasImageContent
}
// 用户输入的内容
const inputContent = ref('')
//被格式化后用户输入的内容，用于判断非空和发送
const sendContent = computed(() => {
  if (isEmptyQuillContent(inputContent.value)) {
    return ''
  } else {
    return inputContent.value
  }
})

let timer: any = null // 定时器
const isTabActive = ref(document.visibilityState === 'visible') // 系统是否在激活状态
onMounted(() => {
  // 监听系统是否在激活状态
  document.addEventListener('visibilitychange', () => {
    isTabActive.value = document.visibilityState === 'visible'
  })
  // 初始化bs并监听resize
  if (!refScroll.value) return
  initScroll()
  observer.value = new ResizeObserver(entries => {
    debounceFn()
  })
  const debounceFn = useDebounceFn(() => {
    scroll.value!.refresh()
  }, 200)
  observer.value.observe(refScroll.value)
  
  // 添加定时器，每秒拉取新消息
  if (timer) return
  timer = setInterval(() => {
    if (!heartbeat.value) return
    getMessageEvent()
  }, 3000)
})
// 销毁定时器
onBeforeUnmount(() => {
  clearInterval(timer)
  timer = null
  if (observer.value) {
    observer.value.disconnect()
  }
  document.removeEventListener('visibilitychange', () => {
    isTabActive.value = document.visibilityState === 'visible'
  })
})

</script>

<style lang="scss" scoped>
.chat-box {
  position: relative;
  display: flex;
  flex-direction: column;
  min-width: 0;
  min-height: 0;
  flex: 1 1 auto;
  height: 100%;
  .chat-content-box {
    flex: 1 1 auto;
    overflow: auto;
    .mouse-wheel-pulldown {
      height: 100%;
      .pulldown-wrapper {
        position: relative;
        height: 100%;
        padding: 0 10px 0 0;
        // border: 1px solid #c0c4cc;
        border-radius: 8px;
        overflow: hidden;
        .pulldown-content {
          box-sizing: border-box;
          padding-top: 1px;
          padding-bottom: 1px; 
          .pulldown-list-item {
            -webkit-animation-duration: 1s;
            animation-duration: 1s;
            -webkit-animation-fill-mode: both;
            animation-fill-mode: both;
            transform-origin: left top;
            border-radius: 8px;
          }
        }
      }
      .pulldown-tips {
        position: absolute;
        width: 100%;
        padding: 20px;
        box-sizing: border-box;
        transform: translateY(-100%) translateZ(0);
        text-align: center;
        color: #999;
        font-size: 12px;
      }
    }
  }
  .paste-box {
    flex: 1 0 auto;
    display: flex;
    overflow-x: auto;
    overflow-y: hidden;
    .paste-img {
      border: 1px solid #dfdcdc;
      position: relative;
      .del-btn {
        width: 20px;
        position: absolute;
        top: 0;
        right: 0;
      }
    }
  }
  .chat-input-box {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: end;
    // margin-top: 10px;
    padding: 12px 2px 8px;
    border-top: 1px solid #c0c4cc;
    background-color: #fff;
    .unread-btn {
      position: absolute;
      right: 0px;
      top: -50px;
      z-index: 999;
      border-radius: 15px;
    }
    .tool-box {
      width: 100px;
      flex-shrink: 0;
      margin-right: 2px;
      line-height: 30px;
      text-align: right;
      .pic-upload {
        display: inline-block;
        font-size: 18px;
        margin: 0;
        height: 31px;
        overflow: hidden;
        float: right;
      }
      .tool-btn {
        border: none;
        font-size: 18px;
        margin: 0;
        height: 32px;
        float: right;
        padding: 2px 5px;
        color:#606266;
      }
      .tool-btn:hover {
        background-color: inherit !important;
        color: #4aa181;
      }
      .is-disabled.send-btn {
        color: #606266 !important;
      }
      .send-btn {
        background-color: inherit !important;
        color: #4aa181 !important;
      }
      .emoji-btn {
        cursor: pointer;
        svg {
          margin-top: 6px;
        }
      }
    }
    .el-textarea {
      flex-grow: 1;
    }
    &:deep(#chat-input) {
      box-shadow: none !important;
      max-height: 40vh;
      resize: none;
      &:hover {
        box-shadow: none !important;
      }
    }
  }
  .btn-box {
    width: 100%;
    background-color: #fff;
    border: 1px solid #c0c4cc;
    border-radius: 8px;
    padding: 5px;
    position: absolute;
    bottom: 0;
    z-index: 100;
    display: flex;
    align-items: center;
    justify-content: center;
    .close-btn {
      position: absolute;
      right: 10px;
      top: 14px;
      cursor: pointer;
    }
  }
  .replay-box {  
    display: flex;
    align-items: center;
    margin-bottom: 3px;
    padding: 4px;
    background-color: #f2f3f5;
    border-radius: 5px;
    font-size: 15px;
    color: #9fa3a8;
    .close-btn {  
      margin: 0 5px;
      padding-right: 5px;
      line-height: 10px;
      border-right: 1px solid #dfe0e1;
      cursor: pointer;
    }
    .message-content {
      display: -webkit-box;
      overflow: hidden;
      text-overflow: ellipsis;
      line-clamp: 1;
      -webkit-line-clamp: 1;
      -webkit-box-orient: vertical;
      .ml-3 {
        margin-left: 3px;
      }
    }
  }
}
.upload-progress {
  width: 160px;
  height: 20px;
  &:deep(.el-progress__text) {
    min-width: 0;
  }
}
@-webkit-keyframes pulse {
  0% {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }

  50% {
    -webkit-transform: scale3d(1.05, 1.05, 1.05);
    transform: scale3d(1.05, 1.05, 1.05);
  }

  100% {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
}

@keyframes pulse {
  0% {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }

  50% {
    -webkit-transform: scale3d(1.05, 1.05, 1.05);
    transform: scale3d(1.05, 1.05, 1.05);
  }

  100% {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
}

.pulse {
  -webkit-animation-name: pulse;
  animation-name: pulse;
  background: #edf6f2;
}
.QC-result-box {
  color: #606266;
  font-size: 18px;
  span {
    font-weight: bold;
  }
}
</style>