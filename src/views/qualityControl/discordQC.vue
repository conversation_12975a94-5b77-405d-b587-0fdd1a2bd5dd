<template>
  <splitpanes class="split-box default-theme" vertical :push-other-panes="false">
    <!-- 左侧内容区域 -->
    <pane size="13.5">
      <splitpanes class="split-box o-left default-theme" horizontal :push-other-panes="false">
        <!-- 数据概览 -->
        <pane>
          <div class="title">
            <span class="iconfont icon-shujugailan"></span>
            <span class="text">{{ $t('text_data_overview') }}</span>
          </div>
          <el-scrollbar>
            <el-button v-for="(v, k) in ovButtonList" @click="shortcutHandle(v)"
              :class="[v.id === activeShortcutId ? 'activeBtn' : '']" :key="k">
              {{ $t(v.lable) }}: {{ overviewData[v.prop] }}
            </el-button>
          </el-scrollbar>
        </pane>
        <!-- 多维数据概览 -->
        <pane>
          <div class="title">
            <span class="iconfont icon-duoweiduziyoupouxi"> </span>
            <span class="text">{{ $t('text_mult_data_overview') }}</span>
          </div>
          <el-scrollbar>
            <el-button v-for="(v, k) in muButtonList" @click="shortcutHandle(v)"
              :class="[v.id === activeShortcutId ? 'activeBtn' : '']" :key="k">
              {{ $t(v.lable) }}: {{ overviewData[v.prop] }}
            </el-button>
          </el-scrollbar>
        </pane>
      </splitpanes>
    </pane>
    <!-- 中间内容区域 -->
    <pane>
      <splitpanes class="split-box default-theme" horizontal :push-other-panes="false">
        <pane size="14">
          <el-scrollbar class="pane-wapper">
            <el-form size="small" ref="searchFormRef" :inline="true" :model="searchForm" class="search-form">
              <!-- 游戏 -->
              <el-form-item prop="project">
                <el-select v-model="searchForm.project" :placeholder="$t('text_game')" multiple collapse-tags
                  collapse-tags-tooltip clearable filterable :reserve-keyword="false">
                  <el-option v-for="(v, index) in gameList" :key="index" :label="v.app_name"
                    :value="v.game_project"></el-option>
                </el-select>
              </el-form-item>
              <!-- 质检单ID -->
              <el-form-item prop="dsc_examine_id">
                <opsInput v-model.number="searchForm.dsc_examine_id" :placeholder="$t('text_QC_ID')" clearable />
              </el-form-item>
              <!-- 质检单状态 -->
              <el-form-item prop="status">
                <el-select v-model="searchForm.status" :placeholder="$t('text_QC_status')" multiple collapse-tags clearable>
                  <el-option v-for="(v, index) in enumList.ExamineState" :key="index" :label="v.name"
                    :value="v.value"></el-option>
                </el-select>
              </el-form-item>
              <!-- 质检结果 -->
              <el-form-item prop="final_result">
                <el-select v-model="searchForm.final_result" :placeholder="$t('text_QC_result')" multiple collapse-tags clearable>
                  <el-option v-for="(v, index) in enumList.ExamineFinalResult" :key="index" :label="v.name"
                    :value="v.value"></el-option>
                </el-select>
              </el-form-item>
              <!-- 被质检人 -->
              <el-form-item prop="related_account">
                <el-select v-model="searchForm.related_account" :placeholder="$t('text_inspected_person')" :reserve-keyword="false" filterable multiple
                  clearable>
                  <el-option v-for="(v, index) in csList" :key="index" :label="v.account"
                    :value="v.account"></el-option>
                </el-select>
              </el-form-item>
              <!-- 质检人 -->
              <el-form-item prop="inspector">
                <el-select v-model="searchForm.inspector" :reserve-keyword="false" :placeholder="$t('text_quality_inspector')" filterable multiple
                  clearable>
                  <el-option v-for="(v, index) in csList" :key="index" :label="v.account"
                    :value="v.account"></el-option>
                </el-select>
              </el-form-item>
              <!-- 创建时间 -->
              <el-form-item prop="created_at">
                <el-date-picker v-model="searchForm.created_at" format="YYYY-MM-DD HH:mm:ss"
                  value-format="YYYY-MM-DD HH:mm:ss" type="datetimerange" :range-separator="$t('to')"
                  :start-placeholder="$t('text_create_time')" :end-placeholder="$t('text_create_time')">
                </el-date-picker>
              </el-form-item>
              <!-- 结案时间 -->
              <el-form-item prop="finished_at">
                <el-date-picker v-model="searchForm.finished_at" format="YYYY-MM-DD HH:mm:ss"
                  value-format="YYYY-MM-DD HH:mm:ss" type="datetimerange" :range-separator="$t('to')"
                  :start-placeholder="$t('text_end_time')" :end-placeholder="$t('text_end_time')">
                </el-date-picker>
              </el-form-item>
              <!-- 搜索操作按钮 -->
              <el-form-item>
                <el-button type="primary" plain icon="Search" @click="search">{{ $t('btn_search') }}</el-button>
              </el-form-item>
              <!-- 重置按钮 -->
              <el-form-item>
                <el-button icon="Refresh" @click="reset">{{ $t('btn_reset') }}</el-button>
              </el-form-item>
              <!-- 下载按钮 -->
              <el-form-item>
                <el-button type="primary" icon="Download" @click="donwLoad" v-has="'qc:discord:download'"
                  :loading="progState" :disabled="progState">{{ $t('btn_download') }}
                  <span v-if="progState">{{ progressNum + '%' }}</span>
                </el-button>
              </el-form-item>
            </el-form>
          </el-scrollbar>
        </pane>
        <pane>
          <el-card class="abbreviation-wapper" shadow="never" v-loading="poolLoading">
            <template #header>
              <slot name="header">
                <span class="list-total-tip">{{ $t('text_result_total', { total: QCListData.total }) }}</span>
              </slot>
            </template>
            <div class="dis-flex">
              <el-scrollbar class="chat-list">
                <el-descriptions v-for="(v, k) in QCListData.list" v-if="QCListData.list.length > 0"
                      @click.stop="activeQCHandle(v)" :key="k" :column="2" size="small" border
                      :id="'item' + v.dsc_user_id + v.dm_channel">
                  <template #title>
                    <div class="qc-list-title">
                      <span class="mr-36">{{ $t('text_QC_ID') }}：{{ v.dsc_examine_id }}</span>
                      <span class="mr-36">{{ $t('text_quality_inspector') }}：{{ v.inspector ? v.inspector : '-' }}</span>
                      <span class="mr-36">{{ $t('text_dc_nick') }}：{{ v.user_name }}</span>
                      <span class="mr-36">UID：{{ v.uid ? v.uid : '-' }}</span>
                      <span class="mr-36">{{ $t('text_server') }}：{{ v.sid ? v.sid : '-' }}</span>
                    </div>
                  </template>
                  <el-descriptions-item label-class-name="my-label">
                      <template #label>
                        <div> {{ $t('text_dc_pw') }} </div>
                      </template>
                      {{ v.dsc_user_id }}
                    </el-descriptions-item>
                    <el-descriptions-item label-class-name="my-label">
                      <template #label>
                        <div> {{ $t('text_game') }} </div>
                      </template>
                      {{ v.project ? v.project : '-' }}
                    </el-descriptions-item>
                    <el-descriptions-item label-class-name="my-label">
                      <template #label>
                        <div> {{ $t('text_total_pay') }} </div>
                      </template>
                      {{ v.total_pay ? v.total_pay : 0 }}$
                    </el-descriptions-item>
                    <el-descriptions-item label-class-name="my-label">
                      <template #label>
                        <div> {{ $t('text_vip_level') }} </div>
                      </template>
                      {{ v.vip_level ? v.vip_level : 0 }}
                    </el-descriptions-item>
                    <el-descriptions-item label-class-name="my-label">
                      <template #label>
                        <div>{{ $t('text_status') }}</div>
                      </template>
                      <span :style="{ color: colorsMap[v.replied_status], 'font-weight': 'bold' }">
                        {{ enumList.DiscordServiceReplyStatus.find((item: Record<string, string | number>) => item.value === v.replied_status)?.name || '-' }}
                      </span>
                    </el-descriptions-item>
                    <el-descriptions-item label-class-name="my-label">
                      <template #label>
                        <div> {{ $t('text_thirty_day_pay') }} </div>
                      </template>
                      {{ v.pay_last_thirty_days ? v.pay_last_thirty_days : 0 }}$
                    </el-descriptions-item>
                    <el-descriptions-item label-class-name="my-label">
                      <template #label>
                        <div> {{ $t('text_last_last_login_time') }} </div>
                      </template>
                      {{ v.last_login ? v.last_login : '-' }}
                    </el-descriptions-item>
                </el-descriptions>
                <el-empty v-else :description="$t('info_no_data')" />
              </el-scrollbar>
            </div>
            <template #footer>
              <el-pagination class="pagination" v-model:current-page="QCListData.page" :small="true"
                :page-sizes="[20, 50, 150, 200, 500, 1000]" v-model:page-size="QCListData.pageSize"
                :total="QCListData.total" layout="total, sizes, prev, pager, next, jumper"
                @size-change="handleSizeChange" @current-change="handleCurrentChange" />
            </template>
          </el-card>
        </pane>
      </splitpanes>
    </pane>
    <pane size="40" style="min-width: 410px;">
      <div class="o-right" v-if="activeQCInfo.dm_channel">
        <el-descriptions class="play-baseinfo" direction="horizontal" size="small" :column="3" border>
          <template #title>
            <div class="qc-list-title">
              <span>{{ $t('text_base_info') }}</span>
              <span style="float: right">{{ $t('text_quality_inspector') }}：{{ activeQCInfo.inspector ?
                activeQCInfo.inspector : $t('text_none') }}</span>
            </div>
          </template>
          <el-descriptions-item>
            <template #label>
              <span> {{ $t('text_game') }} </span>
            </template>
            {{ activeQCInfo.project ? activeQCInfo.project : '-' }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label>
              <span> {{ $t('text_player_nickname') }} </span>
            </template>
            {{ activeQCInfo.user_name ? activeQCInfo.user_name : '-' }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label>
              <span> {{ $t('text_player_dcid') }} </span>
            </template>
            {{ activeQCInfo.dsc_user_id ? activeQCInfo.dsc_user_id : '-' }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label>
              <span> UID </span>
            </template>
            {{ activeQCInfo.uid ? activeQCInfo.uid : '-' }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label>
              <span> Fpid </span>
            </template>
            {{ activeQCInfo.fpid ? activeQCInfo.fpid : '-' }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label>
              <span> {{ $t('text_server') }} </span>
            </template>
            {{ activeQCInfo.sid ? activeQCInfo.sid : '-' }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label>
              <span> {{ $t('text_game_names') }} </span>
            </template>
            {{ activeQCInfo.player_nick ? activeQCInfo.player_nick : '-' }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label>
              <span> {{ $t('text_lang') }} </span>
            </template>
            {{ activeQCInfo.lang ? activeQCInfo.lang : '-' }}
          </el-descriptions-item>
        </el-descriptions>
        <div class="chat-tab-box">
          <el-tabs v-model="activeTab" style="height:100%" v-loading="tabLoading">
            <el-tab-pane :label="$t('text_online_chat')" :name="1">
              <!-- discord模块 -->
              <opsQCDcChat :partner-info="activeQCInfo" v-if="activeQCInfo && activeQCInfo.dm_channel"
                :key="activeQCInfo.dsc_user_id + activeQCInfo.dm_channel" />
            </el-tab-pane>
            <el-tab-pane :label="$t('text_user_portrait')" :name="2" v-loading="portraitLoading">
              <el-scrollbar style="box-sizing: border-box; padding: 0px 12px 0px;">
                <!-- 标签信息 -->
                <div class="common-title-box">
                  <span class="common-title">{{ $t('text_tag_info') }}</span>
                </div>
                <div class="tag-box">
                  <div class="flex" v-if="tagLists.length > 0">
                    <el-tag v-for="(item, index) in tagLists" :key="index" effect="plain" size="small" style="margin: 0 0 5px 10px">
                      {{ item.tag_name }}
                    </el-tag>
                  </div>
                  <div class="no-data-box" v-else><span class="no-data">暂无标签</span></div>
                </div>
                <el-divider border-style="dashed" class="line" />
                <!-- 基础属性 -->
                <div class="common-title-box">
                  <span class="common-title">{{ $t('text_base_attribute') }}</span>
                </div>
                <div class="base-info">
                  <el-form class="base-info-form" label-position="top" size="small" :model="baseForm" ref="formRef"
                    :inline="true">
                    <!-- 性别 -->
                    <el-form-item :label="$t('text_gender')">
                      <opsSelect v-model="baseForm.gender" :placeholder="$t('info_no_data')" :disabled="true"
                        clearable :style="'width: 200px'">
                        <el-option v-for="(v, index) in enumList.PlayerGender" :key="index" :label="v.name"
                          :value="v.value">
                        </el-option>
                      </opsSelect>
                    </el-form-item>
                    <!-- 生日 -->
                    <el-form-item :label="$t('info_no_data')">
                      <el-date-picker
                        v-model="baseForm.birthday"
                        type="date"
                        :placeholder="$t('text_birthday')"
                        format="YYYY-MM-DD"
                        value-format="YYYY-MM-DD"
                        :disabled="true"
                        style="width: 200px"
                      />
                    </el-form-item>
                    <!-- 职业 -->
                    <el-form-item :label="$t('text_career')">
                      <el-input v-model="baseForm.career" :placeholder="$t('info_no_data')"
                        :disabled="true" style="width: 200px" />
                    </el-form-item>
                    <!-- 教育程度 -->
                    <el-form-item :label="$t('text_education_level')">
                      <opsSelect v-model="baseForm.education_level" :placeholder="$t('info_no_data')"
                        :disabled="true" clearable :style="'width: 200px'">
                        <el-option v-for="(v, index) in enumList.PlayerEducationLevel" :key="index" :label="v.name"
                          :value="v.value">
                        </el-option>
                      </opsSelect>
                    </el-form-item>
                    <!-- 婚姻状况 -->
                    <el-form-item :label="$t('text_marital_status')">
                      <opsSelect v-model="baseForm.married_state" :placeholder="$t('info_no_data')"
                        :disabled="true" clearable :style="'width: 200px'">
                        <el-option v-for="(v, index) in enumList.PlayerMarriageState" :key="index" :label="v.name"
                          :value="v.value">
                        </el-option>
                      </opsSelect>
                    </el-form-item>
                    <!-- 生育状况 -->
                    <el-form-item :label="$t('text_ertility_status')">
                      <opsSelect v-model="baseForm.fertility_state" :placeholder="$t('info_no_data')"
                        :disabled="true" clearable :style="'width: 200px'">
                        <el-option v-for="(v, index) in enumList.PlayerFertilityState" :key="index" :label="v.name"
                          :value="v.value">
                        </el-option>
                      </opsSelect>
                    </el-form-item>
                  </el-form>
                </div>
                <el-divider border-style="dashed" class="line" />
                <!-- 备注信息 -->
                <div class="common-title-box">
                  <span class="common-title">{{ $t('text_remark_info') }}</span>
                </div>
                <div class="remark-box">
                  <el-input type="textarea" :rows="4" :disabled="true" :placeholder="$t('info_no_data')"
                    v-model="baseForm.remark">
                  </el-input>
                </div>
              </el-scrollbar>
            </el-tab-pane>
            <!-- 质检信息 -->
            <el-tab-pane :label="$t('text_QC_info')" :name="3" v-loading="QCformLoading">
              <el-scrollbar style="box-sizing: border-box; padding: 0px 12px 0px;">
                <el-form class="QC-info-form" label-width="auto" label-position="top" size="small" :model="QCForm" ref="QCFormRef">
                  <div class="tpl-title">{{ QCformTplInfo.tpl_desc }}</div>
                  <!-- 动态配置表单部分 -->
                  <div class="tpl-form-box" v-if="QCformTplInfo.module_detail && QCformTplInfo.module_detail.length > 0">
                    <div class="form-module-box" v-for="(v, k) in QCformTplInfo.module_detail" :key="k">
                      <div class="module-title"> {{ v.name }}</div>
                      <template v-for="(formItem, index) in QCformTplInfo.module_detail[k].field_detail" :key="''+k+index">
                        <!-- 下拉单选 -->
                        <el-form-item
                          v-if="formItem.field_type === 1"
                          :label="formItem.field_name"
                          :rules="{ required: true, message: $t('place_select')}"
                          :prop="'define_field.' + formItem.field_name"
                        >
                          <el-select v-model="QCForm.define_field[formItem.field_name]" :placeholder="$t('place_select')" clearable :validate-event="false">
                            <el-option v-for="opsItem in formItem.field_opts" :key="opsItem" :label="opsItem" :value="opsItem"></el-option>
                          </el-select>
                        </el-form-item>
                        <!-- 下拉多选 -->
                        <el-form-item
                          v-else-if="formItem.field_type === 2"
                          :label="formItem.field_name"
                          :rules="{ required: true, message: $t('place_select')}"
                          :prop="'define_field.' + formItem.field_name"
                        >
                          <el-select
                            :validate-event="false"
                            v-model="QCForm.define_field[formItem.field_name]"
                            :placeholder="$t('place_select')"
                            multiple
                            collapse-tags
                            clearable
                          >
                            <el-option v-for="opsItem in formItem.field_opts" :key="opsItem" :label="opsItem" :value="opsItem"></el-option>
                          </el-select>
                        </el-form-item>
                        <!-- 单行文本 -->
                        <el-form-item
                          v-else-if="formItem.field_type === 3"
                          :label="formItem.field_name"
                          :rules="{ required: true, message: $t('place_input')}"
                          :prop="'define_field.' + formItem.field_name"
                        >
                          <el-input v-model="QCForm.define_field[formItem.field_name]" :placeholder="$t('place_input')" clearable :validate-event="false" />
                        </el-form-item>
                        <!-- 多行文档 -->
                        <el-form-item
                          v-else-if="formItem.field_type === 4"
                          :label="formItem.field_name"
                          :rules="{ required: true, message: $t('place_input')}"
                          :prop="'define_field.' + formItem.field_name"
                        >
                          <el-input
                            v-model="QCForm.define_field[formItem.field_name]"
                            :validate-event="false"
                            :placeholder="$t('place_input')"
                            type="textarea"
                            :rows="4"
                            clearable
                          />
                        </el-form-item>
                      </template>
                    </div>
                  </div>
                  <el-divider border-style="dashed" />
                  <!-- 公共表单部分 -->
                  <div class="common-form-box">
                    <!-- 是否通过本次质检 -->
                    <el-form-item
                      :label="$t('text_QC_ispass')"
                      prop="common_field.final_result"
                      :rules="{
                        required: true,
                        message: $t('place_select'),
                        validator: (r, val, cb) => {
                          if (val === '' || val === 0) {
                            cb(new Error($t('place_select')))
                          } else {
                            cb()
                          }
                        }
                      }"
                    >
                      <el-radio-group v-model="QCForm.common_field.final_result" :validate-event="false">
                        <el-radio v-for="(i, key) in enumList.ExamineFinalResult" :key="key" :label="i.name" :value="i.value" />
                      </el-radio-group>
                    </el-form-item>
                    <!-- 质检分数 -->
                    <el-form-item
                      :label="$t('text_QC_score')"
                      prop="common_field.final_score"
                    >
                      <el-input-number
                        v-model.number="QCForm.common_field.final_score"
                        :placeholder="$t('place_input')"
                        :min="1"
                        :max="100"
                        controls-position="right"
                      />
                    </el-form-item>
                    <!-- 错误根源 -->
                    <el-form-item
                      :label="$t('text_error_source')"
                      prop="common_field.final_reason"
                    >
                      <el-select v-model="QCForm.common_field.final_reason" :placeholder="$t('place_select')" clearable>
                        <el-option v-for="(v, index) in enumList.ExamineFinalReason" :key="index" :label="v.name"
                          :value="v.value">
                        </el-option>
                      </el-select>
                    </el-form-item>
                    <!-- 质检结果关联员工 -->
                    <el-form-item :label="$t('text_QC_result_person')" prop="common_field.related_account" :rules="{ required: true, message: $t('place_select')}">
                      <el-select v-model="QCForm.common_field.related_account" :placeholder="$t('place_select')" :reserve-keyword="false" filterable multiple clearable :validate-event="false">
                        <el-option v-for="(v, index) in csList" :key="index" :label="v.account" :value="v.account"></el-option>
                      </el-select>
                    </el-form-item>
                    <!-- 同步质检结果 -->
                    <el-form-item :label="$t('text_QC_result_to')" prop="common_field.notice_account">
                      <el-select v-model="QCForm.common_field.notice_account" :placeholder="$t('place_select')" :reserve-keyword="false" filterable multiple clearable>
                        <el-option v-for="(v, index) in csList" :key="index" :label="v.account" :value="v.account"></el-option>
                      </el-select>
                    </el-form-item>
                    <!-- 质检问题描述 -->
                    <el-form-item :label="$t('text_QC_problem_description')" prop="common_field.final_desc">
                      <el-input v-model="QCForm.common_field.final_desc" :placeholder="$t('place_input')" type="textarea" :rows="4" clearable />
                    </el-form-item>
                    <!-- 修改原因备注 -->
                    <el-form-item v-if="QCForm.status === 100" :label="$t('text_modification_cause_remarks')" prop="common_field.final_result_modify_comment">
                      <el-input v-model="QCForm.common_field.final_result_modify_comment" :placeholder="$t('place_input')" type="textarea" :rows="4" clearable />
                    </el-form-item>
                    <el-form-item v-if="QCForm.can_edited">
                      <el-button v-has="'qc:discord:qcsubmit'" type="primary" class="QC-btn" @click="submit">{{ QCForm.status === 100 ? $t('btn_edit_QC') : $t('btn_submit_QC') }}</el-button>
                    </el-form-item>
                  </div>
                </el-form>
              </el-scrollbar>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
    </pane>
  </splitpanes>
</template>

<script lang="ts">
import type { FormInstance } from 'element-plus'
import { Splitpanes, Pane } from 'splitpanes'
import 'splitpanes/dist/splitpanes.css'
import { useI18n } from 'vue-i18n' // 国际化
import { useEnumStore, useUserInfoStore, useAppStore } from '@/stores'
import { getPortrait } from '@/api/chatting'
import { getAcceptorList } from '@/api/assignConfig'
import { discordQcTaskList, getQcformTpl, getQcTaskDetail, saveQcTask, getQcOverview, qcTaskDownload } from '@/api/discordQC'
import opsQCDcChat from './components/opsQCDcChat.vue'

interface FormTplInfoT {
  tpl_desc?: string
  module_detail?: Array<{
    name: string
    field_detail: Array<{
      field_name: string //字段key
      field_type: 1 | 2 | 3 | 4 //表单类型 - 1: 下拉单选 - 2: 下拉多选 - 3: 单行文本 - 4: 多行文档
      field_opts: string[] //表单选项(类型为select时)
    }>
  }>
}
interface SearchForm {
  project: string[]
  dsc_examine_id?: number
  status?: number[]
  final_result?: number[]
  related_account?: string[]
  inspector?: string[]
  created_at?: string[]
  finished_at?: string[]
}
interface LabelObject {  
  id: number
  tag_name: string 
}
interface BaseForm {
  tag: number[]
  id: number
  dsc_user_id: string
  fpid: string
  game_project: string
  label?: LabelObject[]
  gender?: number
  birthday: string
  career: string
  education_level?: number
  married_state?: number
  fertility_state?: number
  remark: string
}
interface shortcutButtonT {
  id: number
  lable: string
  shortcut: Record<string, unknown>
}
type SearchFormKey = keyof SearchForm
type SearchFormValue = SearchForm[keyof SearchForm]
export default defineComponent({
  name: 'DiscordQC',
  components: { Splitpanes, Pane, ElMessage, opsQCDcChat }
})
</script>
<script setup lang="ts">
const { t: $t } = useI18n()
const colorsMap = {
  1: '#E6A23C', // 待处理-黄色
}
const gameList = computed(() => useEnumStore().gameList)
const enumList = computed(() => useEnumStore().enumList)
const userInfo = computed(() => useUserInfoStore().userInfo as Record<string, unknown>)

// 下载进度
const progressNum = computed(() => useAppStore().progressNum)
const progState = ref<boolean>(false)
watch(progressNum, (n) => {
  progState.value = (n < 100 && n > -1) ? true : false
})

const csList = ref<Record<string, string>[]>([])
const getCsList = async () => {
  const res = await getAcceptorList({})
  csList.value = res
}
getCsList()

const isTimerActive = ref(true) // 表示定时任务应该运行

// 点击具体聊天对象
const activeQCInfo = ref<Record<string, any>>({})
const tagLists = ref<Array<{id: number, tag_name: string}>>([])
const activeQCHandle = (v: Record<string, unknown>) => {
  activeQCInfo.value = {}
  activeQCInfo.value = v
  v.showMarkInput = false
  tagLists.value = []
  getPlayerInfo()
  getQcformTplData()
}
const portraitLoading = ref(false)
const baseForm = ref<BaseForm>({} as BaseForm)
// 获取玩家画像
const getPlayerInfo =() => {
  portraitLoading.value = true
  tagLists.value = []
  getPortrait({
    dsc_user_id: activeQCInfo.value.dsc_user_id,
    game_project: activeQCInfo.value.project
  }).then((res: any) => {
    if (res.label) {
      tagLists.value = res.label
    }
    baseForm.value = { ...res }
  }).catch((error: Error) => {  
    console.error('Failed to get player info:', error)
  }).finally(() => {
    portraitLoading.value = false
  })
}
// 获取质检单模板
const QCformLoading = ref(false)
const QCformTplInfo = ref<FormTplInfoT>({}) // 质检表单模板信息
// 质检单模块
const QCForm = ref<any>({
  common_field: {
    final_result: 1,
    related_account: [],
    notice_account: [],
    final_desc: '',
    final_score: 1,
    final_reason: '',
    final_result_modify_comment: ''
  },
  define_field: {}
})
const QCFormRef = ref<FormInstance>()
const getQcformTplData = () => {
  QCformLoading.value = true
  QCformTplInfo.value = {}
  QCForm.value = {
    common_field: {
      final_result: 1,
      related_account: [],
      notice_account: [],
      final_desc: '',
      final_score: 1,
      final_reason: '',
      final_result_modify_comment: ''
    },
    define_field: {}
  }
  getQcformTpl({
    tpl_id: activeQCInfo.value.tpl_id
  }).then((res: any) => {
    QCformTplInfo.value = res
    getQcTaskDetail({
      dsc_examine_id: activeQCInfo.value.dsc_examine_id
    }).then((r: any) => {
      QCForm.value = r
      if (activeQCInfo.value.status !== 100) {
        // 未质检的设置默认值
        if (res.module_detail && res.module_detail.length > 0) {
          res.module_detail.forEach((module: any) => {
            module.field_detail.forEach((formItem: any) => {
              if (formItem.field_opts && formItem.field_opts.length > 0) {
                if (formItem.field_type === 2) {
                  QCForm.value.define_field[formItem.field_name] = [formItem.field_opts[0]]
                } else {
                  QCForm.value.define_field[formItem.field_name] = formItem.field_opts[0]
                }
              }
            })
          })
        }
      }
    }).catch((error: Error) => {
      console.error('Failed to get qc form tpl:', error)
    }).finally(() => {
      QCformLoading.value = false
    })
  }).catch((error: Error) => {
    console.error('Failed to get qc form tpl:', error)
  })
}
// 提交质检单
const submit = () => {
  QCFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      QCformLoading.value = true
      saveQcTask({
        ...QCForm.value
      }).then(() => {
        ElMessage.success($t('text_submit_success'))
        getQcTaskDetail({
          dsc_examine_id: activeQCInfo.value.dsc_examine_id
        }).then((r: any) => {
          QCForm.value = r
        }).catch((error: Error) => {
          console.error('Failed to get qc form tpl:', error)
        }).finally(() => {
          QCformLoading.value = false
        })
      }).catch((error: Error) => {
        console.error('Failed to save qc form:', error)
      })
    }
  })
}


// 聊天列表list自动滚动active item功能
const refreshTable = () => {
  if (activeQCInfo.value) {
    const element = document.querySelector(`#item${activeQCInfo.value.dsc_user_id}${activeQCInfo.value.dm_channel}`)
    if (element) {
      element.scrollIntoView()
    }
  }
}

//默认查询条件
const searchFormInitData = {
  project: [],
  status:[],
  final_result: [],
  related_account: [],
  inspector: [],
  created_at: [],
  finished_at: []
}
const searchFormRef = ref<FormInstance>()
const searchForm = ref<SearchForm>({ ...searchFormInitData })

// 查询质检单列表
const QCListData = reactive({
  list: [] as Record<string, unknown>[], // 表格数据
  total: 0, // 总数
  page: 1, // 当前页
  pageSize: 20, // 每页条数
})
const poolLoading = ref(false)
const search = async () => {
  poolLoading.value = true
  const params = Object.assign({}, searchForm.value, {
    page: QCListData.page,
    page_size: QCListData.pageSize
  })
  try {
    const res = await discordQcTaskList(params)
    QCListData.list = res.data
    QCListData.total = res.total
  } finally {
    poolLoading.value = false
    refreshTable()
  }
}
// 下载
const donwLoad = () => {
  qcTaskDownload({...searchForm.value})
}

const handleSizeChange = (val: number) => {
  QCListData.page = 1
  QCListData.pageSize = val
  search()
}
const handleCurrentChange = (val: number) => {
  QCListData.page = val
  search()
}

const activeShortcutId = ref(0)
const shortcutHandle = (v: shortcutButtonT) => {
  activeShortcutId.value = v.id
  searchForm.value = { ...searchFormInitData }
  Object.keys(v.shortcut).forEach((key) => {
    if (key in searchForm.value) {
      if (key === 'inspector' && v.shortcut[key] === 'CurrentUser') {
        (searchForm.value[key as SearchFormKey] as SearchFormValue) = Array.of(userInfo.value.username) as SearchFormValue
      } else {
        (searchForm.value[key as SearchFormKey] as SearchFormValue) = v.shortcut[key] as SearchFormValue
      }
    }
  })
  search()
}
// 重置按钮
const reset = () => {
  searchFormRef.value?.resetFields()
  shortcutHandle(ovButtonList[0])
}

// 右侧切换tab
const activeTab = ref(1)
provide('activeTab', activeTab)
const tabLoading = ref(false)

// 数据概览快捷查询按钮
const ovButtonList = [
  {
    id: 1,
    lable: 'text_total_QCtask',
    shortcut: searchFormInitData,
    prop: 'examine_dsc_count'
  },
  {
    id: 2,
    lable: 'text_pending_QCtask',
    shortcut: {
      status: [1]
    },
    prop: 'examine_dsc_wait_count'
  },
  {
    id: 3,
    lable: 'text_pending_QCtask_my',
    shortcut: {
      status: [1],
      inspector: 'CurrentUser'
    },
    prop: 'examine_dsc_mine_wait_count'
  },
  {
    id: 4,
    lable: 'text_finish_QCtask_my',
    shortcut: {
      status: [100],
      inspector: 'CurrentUser'
    },
    prop: 'examine_dsc_mine_done_count'
  }
]
const muButtonList = [
  {
    id: 5,
    lable: 'text_QC_failure_task',
    shortcut: {
      final_result: [2]
    },
    prop: 'examine_dsc_unqualified_count'
  },
  {
    id: 6,
    lable: 'text_QC_failure_task_my',
    shortcut: {
      final_result: [2],
      inspector: 'CurrentUser'
    },
    prop: 'examine_dsc_mine_unqualified_count'
  }
]

const overviewData: Record<string, number> = reactive({
  examine_dsc_count: 0,
  examine_dsc_wait_count: 0,
  examine_dsc_mine_wait_count: 0,
  examine_dsc_mine_done_count: 0,
  examine_dsc_unqualified_count: 0,
  examine_dsc_mine_unqualified_count: 0
})

// 获取概览数据
const getOverview = async() => {
  try {
    const res = await getQcOverview({})
    Object.keys(overviewData).forEach((key) => {
      overviewData[key] = res[key]
    })
  } catch (error) {
    console.log(error)
  }
}
getOverview()

let timer: any = null
onMounted(async () => {
  const route = useRouter()
  if (route.currentRoute.value.query && route.currentRoute.value.query.id) {
    searchForm.value.dsc_examine_id = Number(route.currentRoute.value.query.id)
    activeTab.value = 3
    await search()
    if (QCListData.list.length > 0) {
      activeQCHandle(QCListData.list[0])
    } else {
      ElMessage.error('数据错误，并未找到对应的质检单')
    }
  } else {
    searchForm.value = { ...searchFormInitData }
    search()
  }
  timer = setInterval(() => {
    if(isTimerActive.value) {// 确保在每次执行前检查状态  
      search()
      getOverview()
    }
  }, 30000)
})
onUnmounted(() => {
  clearInterval(timer)
})
</script>

<style lang="scss" scoped>
.split-box {
  height: 100%;
  .pane-wapper {
    box-sizing: border-box;
    padding: 10px 5px;
    .hor-line {
      margin: 0 2px;
      font-size: 16px;
      font-weight: bold;
      color: #606266;
    }
    &:deep(.el-input-number) {
      .el-input{
        min-width: 40px;
      }
    }
    &:deep(.el-input-number--small) {
      // width: 50px;
      .el-input-number__increase {
        display: none !important;
      }
      .el-input-number__decrease {
        display: none !important;
      }
    }
  }

  .o-left {
    .el-scrollbar {
      height: calc(100% - 62px);
    }
    &:deep(.el-sub-menu__title) {
      height: 40px;
      line-height: 40px;
      &:hover {
        background-color: transparent;
      }
    }
    &:deep(.el-menu-item) {
      padding: 0;
      &:hover {
        background-color: transparent;
      }
    }
    .delete-btn {
      padding: 5px;
      position: absolute;
      right: -10px;
      visibility: hidden;
    }
    &:deep(.el-button) {
      &:hover {
        .delete-btn {
          visibility: visible !important;
          color: #4aa181;
          background-color: #edf6f2;
          transition: background-color 0.2s
        }
      }
    }
    .activeBtn {
      color: var(--el-button-hover-text-color);
      border-color: var(--el-button-hover-border-color);
      background-color: var(--el-button-hover-bg-color);
      outline: none;
    }

    .el-button {
      display: block;
      margin: 2px auto 20px;
      width: 90%;
      overflow: hidden;
    }

    .title {
      font-size: 18px;
      height: 40px;
      line-height: 40px;
      font-weight: bold;
      text-align: center;
      -webkit-background-clip: text;
      background-clip: text;
      color: transparent;
      background-image: linear-gradient(to right, rgba(39, 177, 236, 0.8), rgba(74, 161, 129, 0.8));
      box-shadow: 0px 2px 7px -3px rgba(0, 0, 0, 0.6);
      margin-bottom: 20px;

      .text {
        margin-left: 10px;
      }
    }
  }
}
.abbreviation-wapper {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  .dis-checkbox {
    float: left;
    margin-right: 5px;
  }
  .list-total-tip {
    float: left;
    margin-top: 5px;
    margin-bottom: 10px;
    font-size: 14px;
    font-weight: bold;
    color: var(--el-text-color-regular);
  }
  &:deep(.my-label) {
    width: 120px;
    // text-align: right;
  }
  &:deep(.right-label) {
    // width: 120px;
    text-align: right;
  }
  &:deep(.el-form--inline .el-form-item) {
    margin-bottom: 10px;
    margin-right: 0px;
  }
  &:deep(.el-card__header, .el-card__footer) {
    padding: 10px 10px 0px;
  }

  &:deep(.el-card__footer) {
    border: 0px;
    padding: 10px;
  }

  &:deep(.el-card__body) {
    flex-grow: 1;
    padding: 10px 0px;
    overflow: hidden;
    .el-descriptions {
      cursor: pointer;
      width: 96%;
      margin: 0px auto 30px;
      padding: 8px;
      border-radius: 5px;
      background-color: #f8f9fa;
    }
  }
  .dis-flex {
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;

    .chat-list {
      width: 100%;
      flex-grow: 1;
      .mark-color {
        color: green;
        white-space: normal;
        text-align: left;
        line-height: 1.5rem;
      }
    }
  }
  .pagination {
    float: right;
  }
}
.qc-list-title {
  margin-left: 2px;
  span {
    display: inline-block;
    height: 32px;
    line-height: 32px;
  }
  .mr-36 {
    margin-right: 36px;
  }
}
.base-info-form, .QC-info-form {
  .el-form-item {
    margin: 8px 10px 15px;
  }
}
.o-right {
  height: 100%;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  padding: 5px 15px;
  
  .play-baseinfo {
    width:100%;
    &:deep(.el-descriptions__title) {
      width: 100% !important;
    }
    &:deep(.el-descriptions__header) {
      margin-bottom: 0px;
    }
  }
  .chat-tab-box {
    width: 100%;
    flex-grow: 1;
    overflow: auto;
    .el-tabs {
      height: 100%;
      display: flex;
      flex-direction: column;
      &:deep(.el-tabs__content) {
        flex: 1;
      }
      .el-tab-pane {
        height: 100%;
        overflow: auto;
        
      }
    }
    .common-title-box {
      display: flex;
      justify-content: space-between;
      height: 24px;
      line-height: 24px;
      margin-top: 10px;
      .common-title{
        font-size: 14px;
        font-weight: bold;
      }
    }
    .tag-box{
      max-height: 100px;
      overflow: auto;
      position: relative;
      margin-top: 10px;
      .no-data-box {
        height: 30px;
      }
      .no-data{
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-size: 14px;
        color: #909399;
      }
    }
    .base-info{
      margin-top: 10px;
      padding-left: 10px;
    }
    .remark-box{
      margin-top: 10px;
    }
  }
}
.tpl-title {
  font-size: 20px;
  font-weight: bold;
  margin: 2px auto 12px;
  text-align: center;
}
.module-title {
  font-size: 14px;
  font-weight: bold;
  margin: 10px 0;
}
.QC-btn {
  width: 80%;
  margin: 0 auto;
}
</style>