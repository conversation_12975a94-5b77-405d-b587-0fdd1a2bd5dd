// 全量引入
// import * as echarts from 'echarts'

// 按需引入
import * as echarts from 'echarts/core';
// 引入柱状图、折线图、饼图、热力图
import { BarChart, LineChart, PieChart, HeatmapChart } from 'echarts/charts';
import {
  GraphicComponent,
  GridComponent,
  LegendComponent,
  PolarComponent,
  TitleComponent,
  TooltipComponent,
  DataZoomComponent,
  ToolboxComponent,
} from 'echarts/components';
import { UniversalTransition } from 'echarts/features';
import { CanvasRenderer } from 'echarts/renderers';

echarts.use([
  GraphicComponent,
  GridComponent,
  LegendComponent,
  PolarComponent,
  TitleComponent,
  TooltipComponent,
  <PERSON><PERSON>hart,
  LineChart,
  PieChart,
  HeatmapChart,
  UniversalTransition,
  CanvasRenderer,
  DataZoomComponent,
  ToolboxComponent,
]);

export default echarts;
