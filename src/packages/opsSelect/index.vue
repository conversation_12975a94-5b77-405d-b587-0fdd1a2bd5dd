/* * @Author: wenh<PERSON>.wang * @Date: 2024-04-30 19:34:09 * @Last Modified by: wenhao.wang * @Last
Modified time: 2024-04-30 19:45:21 */
<template>
  <el-select
    v-model="internalValue"
    :style="style"
    :placeholder="placeholder"
    :clearable="clearable"
  >
    <slot></slot>
  </el-select>
</template>

<script setup lang="ts">
const props = defineProps({
  modelValue: {
    type: Number,
    default: 0,
  },
  placeholder: {
    type: String,
    default: '请选择',
  },
  style: {
    type: String,
    default: '',
  },
  clearable: {
    type: Boolean,
    default: true,
  },
});

const emit = defineEmits(['update:modelValue']);

const internalValue = computed({
  get: () => (props.modelValue === 0 ? '' : props.modelValue),
  set: value => {
    emit('update:modelValue', value === '' || value === undefined ? 0 : value);
  },
});
</script>
