/* * @Author: wenh<PERSON>.wang * @Date: 2024-10-28 18:14:06 * @Last Modified by: wenhao.wang * @Last
Modified time: 2024-11-04 11:38:20 */
<template>
  <div
    class="lot-canvas"
    :id="props.msgId + props.stickerData.id"
    v-if="props.stickerData.format_type === 3"
  ></div>
  <div class="image-canvas" v-else-if="props.stickerData.format_type === 1">
    <img :src="`https://discord.com/stickers/${props.stickerData.id}.png`" alt="图片" />
  </div>
  <div v-else>暂不支持的表情类型,请联系wenhao.wang</div>
</template>

<script lang="ts">
import lottie from 'lottie-web';
import { getProxyResource } from '@/api/chatting';
export default defineComponent({
  name: 'StickersReader',
});
interface StickerData {
  format_type: number;
  id: string;
  name: string;
}
interface StickersReaderProps {
  stickerData: StickerData;
  msgId: string;
}
</script>
<script setup lang="ts">
const props = defineProps<StickersReaderProps>();
const getLotData = async (id: string) => {
  const res = await getProxyResource({
    url: `https://discord.com/stickers/${id}.json`,
  });
  const animationData = JSON.parse(res);
  const container = document.getElementById(props.msgId + props.stickerData.id) as HTMLElement;
  lottie.loadAnimation({
    container: container,
    renderer: 'canvas',
    loop: true,
    autoplay: true,
    animationData: animationData,
  });
};
onMounted(() => {
  if (props.stickerData.format_type === 3) {
    getLotData(props.stickerData.id);
  }
});
</script>

<style lang="scss" scoped>
.lot-canvas,
.image-canvas {
  width: 200px;
  height: 200px;
}
.image-canvas {
  img {
    width: 100%;
    height: 100%;
    display: block;
  }
}
</style>
