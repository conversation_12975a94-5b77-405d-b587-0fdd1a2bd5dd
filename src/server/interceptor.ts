/*
 * @Author: wenh<PERSON>.wang
 * @Date: 2024-04-16 20:00:33
 * @Last Modified by: wenhao.wang
 * @Last Modified time: 2025-01-10 15:42:32
 */
import { useAppStore } from '@/stores/app';
import axios, {
  type AxiosError,
  type AxiosInstance,
  type AxiosResponse,
  type AxiosStatic,
} from 'axios';
import { ElMessage } from 'element-plus';

export default class FpAxios {
  public static server: AxiosInstance;

  public static init(): AxiosStatic {
    this.server = axios.create({
      timeout: 6000000,
      onDownloadProgress: e => {
        if (e.event.target.responseType !== 'arraybuffer') return;
        const startTime = useAppStore().startTime;
        console.log(startTime, Date.now() - startTime);
        const elapsedTime = (Date.now() - startTime) / 1000;
        const progress = Math.min(100, Math.floor(100 / (1 + Math.exp(-elapsedTime / 5 + 2))));
        useAppStore().Progress(progress);
      },
    });
    this.serverInterceptors();
    return axios;
  }

  // interceptors
  public static serverInterceptors(): void {
    // request
    this.server.interceptors.request.use(
      config => {
        return config;
      },
      (error: AxiosError) => {
        console.log(error); // for debug
        return Promise.reject(error);
      }
    );

    // response
    this.server.interceptors.response.use(
      (response: AxiosResponse) => {
        if (response.status !== 200) {
          ElMessage.error('接口错误：status' + response.status);
          return Promise.reject('接口错误：status' + response.status);
        }
        if (response.data instanceof ArrayBuffer) {
          return response;
        }
        if (
          response.config.url?.includes('dsc_bot_config/check') ||
          response.config.url?.includes('dsc_bot_config/add') ||
          response.config.url?.includes('dsc_bot_config/update_welcome_message') ||
          response.config.url?.includes('v2/threads/detail')
        ) {
          return response;
        }
        if (response.data.code !== 0) {
          if (response.data.code === 2000) {
            useAppStore().logOut();
          } else {
            ElMessage.error('接口错误：' + response.data.msg);
            return Promise.reject('接口错误：' + response.data.msg);
          }
        }
        if (response.data && response.data.data) return response.data.data;
        return response.data;
      },
      (error: AxiosError) => {
        return Promise.reject(error);
      }
    );
  }
}
