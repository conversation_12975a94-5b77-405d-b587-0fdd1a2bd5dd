declare module 'splitpanes' {
  import { DefineComponent } from 'vue';

  export const Splitpanes: DefineComponent<{
    vertical?: boolean;
    horizontal?: boolean;
    pushOtherPanes?: boolean;
    dblClickSplitter?: boolean;
    resizerStyle?: Record<string, any>;
    paneStyle?: Record<string, any>;
    [key: string]: any;
  }>;

  export const Pane: DefineComponent<{
    size?: number | string;
    minSize?: number | string;
    maxSize?: number | string;
    [key: string]: any;
  }>;
}
