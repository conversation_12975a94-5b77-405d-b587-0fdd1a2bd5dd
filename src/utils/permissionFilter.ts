/*
 * @Author: we<PERSON><PERSON>.wang
 * @Date: 2024-04-11 17:58:56
 * @Last Modified by: wenhao.wang
 * @Last Modified time: 2024-04-11 18:07:32
 */

import { type RouteRecordRaw } from 'vue-router';
/**
 *
 * @param  {Array} permission 后台返回的用户权限
 * @param  {Array} allRouter  项目全量路由
 * @return {Array} realRoutes 过滤后的路由
 */

export function recursionRouter(permission: string[] = [], allRouter: RouteRecordRaw[] = []) {
  let realRoutes = [];
  realRoutes = allRouter.filter(item => {
    if (permission.indexOf(item.path) > -1) {
      if (item.children && item.children.length) {
        const _child = recursionRouter(permission, item.children);
        if (_child.length > 0) {
          item.children = _child;
        } else {
          delete item.children;
        }
      }
      return item;
    }
  });
  setDefaultRoute(realRoutes);
  return realRoutes;
}

/**
 *
 * @param {Array} routes 用户过滤后的路由
 *
 * 递归为所有有子路由的路由设置第一个children.path为默认路由
 */
export function setDefaultRoute(routes: RouteRecordRaw[]) {
  routes.forEach(v => {
    if (v.children && v.children.length > 0) {
      v.redirect = v.children[0].path;
      setDefaultRoute(v.children);
    }
  });
}
