import 'normalize.css/normalize.css'; // default css
import '@/assets/styles/index.scss'; // global css

import { createApp } from 'vue';
import * as ElementPlusIconsVue from '@element-plus/icons-vue';
import store from './stores';
import i18n from './plugins/i18n';
import FpAxios from './server/interceptor';
import btnPermission from './utils/btnPermission';
import resize from './utils/resize';
import { saveUrlParamsFromRoute } from './utils/urlParams';

import App from './App.vue';
import router from './router';

console.log('ops-ticket-web 测试发布', 20250410);

// 保存URL参数到sessionStorage，确保登录后也能使用
saveUrlParamsFromRoute();

const app = createApp(App);
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component);
}

app.use(store);
app.use(router);
app.use(i18n);
app.use(btnPermission);
app.use(resize);

app.mount('#app');
FpAxios.init();
