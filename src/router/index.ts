/*
 * @Author: wenhao.wang
 * @Date: 2024-04-11 16:44:50
 * @Last Modified by: wenhao.wang
 * @Last Modified time: 2024-04-13 16:04:21
 */
import { createRouter, createWebHistory, type RouteRecordRaw } from 'vue-router';
import NProgress from 'nprogress';
import 'nprogress/nprogress.css';
import { ElMessage } from 'element-plus';
import { useUserInfoStore } from '@/stores';

import baseRouter from './baseRouters';

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: baseRouter,
});

NProgress.configure({ showSpinner: false });
let bool = false;
router.beforeEach(async (to, from, next) => {
  if (to.fullPath === '/login') {
    localStorage.clear();
    sessionStorage.removeItem('path');
    return next();
  }
  if (to.path !== '/accessDeny' && to.path !== '/' && to.path !== '/login')
    sessionStorage.setItem('path', to.fullPath);
  NProgress.start();
  const store = useUserInfoStore();
  if (to.query.token) {
    localStorage.setItem('token', to.query.token.toString());
    next({ name: 'Home' });
    return;
  }

  if (localStorage.getItem('token')) {
    if (!bool) {
      // 获取权限
      bool = true;
      await store.setUserTocken(localStorage.getItem('token') as string);
    }
    return next();
  } else {
    ElMessage.error('未查询到本地的登录凭证，请先登录！');
    return next('/login');
  }
});

router.afterEach(() => {
  NProgress.done();
});

export default router;
