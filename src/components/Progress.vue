/* * @Author: wenh<PERSON>.wang * @Date: 2024-07-08 16:18:11 * @Last Modified by: wenhao.wang * @Last
Modified time: 2024-07-08 16:23:25 */

<template>
  <div class="loding" v-drag v-if="process > -1">
    <el-progress
      :text-inside="true"
      :stroke-width="24"
      :format="proformat"
      :percentage="process"
      status="success"
    ></el-progress>
  </div>
  <div class="fpshade"></div>
</template>

<script setup lang="ts">
import { computed, watch, type Directive } from 'vue';
import { useAppStore } from '@/stores';

const process = computed(() => useAppStore().progressNum);

watch(process, n => {
  // 下载完成后进度条消失
  if (n === 100) {
    setTimeout(() => {
      useAppStore().Progress(-1);
    }, 2000);
  }
});

const proformat = (percentage: number) =>
  percentage === 100 ? `${percentage}% 下载完成` : `${percentage}%`;

// 自定义拖拽指令
const vDrag: Directive<any, void> = (el: HTMLElement) => {
  el.onmousedown = e => {
    // 打开遮罩层
    (document.querySelector('.fpshade') as HTMLElement).style.display = 'block';
    // 算出鼠标相对元素的位置
    const disX = e.clientX - el.offsetLeft;
    const disY = e.clientY - el.offsetTop;
    const maxY = document.body.clientWidth - el.offsetWidth;
    const maxX = document.body.clientHeight - el.offsetHeight;
    document.onmousemove = e => {
      // 用鼠标的位置减去鼠标相对元素的位置，得到元素的位置
      let left = e.clientX - disX;
      let top = e.clientY - disY;
      // 设置边缘
      left = left > maxY ? maxY : left;
      left = left < 0 ? 0 : left;
      top = top > maxX ? maxX : top;
      top = top < 0 ? 0 : top;
      // 移动当前元素
      el.style.left = left + 'px';
      el.style.top = top + 'px';
    };
    document.onmouseup = () => {
      document.onmousemove = () => {};
      document.onmouseup = () => {};
      // 关闭遮罩
      (document.querySelector('.fpshade') as HTMLElement).style.display = 'none';
    };
  };
};
</script>

<style lang="scss" scoped>
.loding {
  position: fixed;
  bottom: 10px;
  right: 20px;
  height: 24px;
  width: 130px;
  z-index: 6001;
  opacity: 0.8;
  cursor: move;
  border-radius: 24px;
  box-shadow: 0px 1px 7px 1px rgba(0, 0, 0, 0.8);
  user-select: none;

  .el-progress-bar__outer {
    background-color: rgba(0, 0, 0, 0.8);
  }
}

.fpshade {
  position: absolute;
  top: 0px;
  bottom: 0px;
  left: 0px;
  right: 0px;
  z-index: 6000;
  height: 100%;
  width: 100%;
  display: none;
}
</style>
