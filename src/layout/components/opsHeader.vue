<template>
  <div class="header-box">
    <div class="logo"></div>
    <div class="line"></div>
    <div class="sys-name">{{ $t('title') }}</div>
    <div class="handle-box">
      <!-- 全屏按钮 -->
      <div class="full-screen" @click="toggleFullScreen">
        <el-tooltip
          :content="!isfull ? $t('text_full_screen') : $t('text_close_full_screen')"
          placement="bottom"
          effect="light"
        >
          <span v-if="isfull" class="iconfont icon-full-screen-exit"></span>
          <span v-else class="iconfont icon-quanping"></span>
        </el-tooltip>
      </div>
      <div class="line"></div>
      <!-- 多语言切换 -->
      <el-dropdown class="lang-info">
        <span class="lang">
          {{ activeLang }}
          <el-icon class="el-icon--right">
            <arrow-down />
          </el-icon>
        </span>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item
              v-for="lang in langList"
              :key="lang.code"
              @click="changeLang(lang.code)"
            >
              {{ lang.name }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
      <div class="line"></div>
      <el-tooltip :content="$t('text_logout')" placement="bottom" effect="light">
        <el-link class="user-info" type="primary" :underline="false" @click="logOut">
          <div class="user">
            <el-icon>
              <SwitchButton />
            </el-icon>
          </div>
        </el-link>
      </el-tooltip>
      <!-- 用户信息 -->
      <!-- <el-dropdown class="user-info">
        <span class="user">
          {{ userInfo.username }}
          <el-icon>
            <arrow-down />
          </el-icon>
        </span>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item icon="SwitchButton" @click.native="logOut">
              {{$t('text_logout')}}
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown> -->
    </div>
  </div>
</template>

<script lang="ts">
import { useLangsStore, useAppStore, useUserInfoStore, useEnumStore } from '@/stores';
import screenfull from 'screenfull';
import langConfig from '@/assets/lang';
import { online } from '@/api/common';
export default defineComponent({
  name: 'OpsHeader',
});
</script>
<script setup lang="ts">
const reload = ref<any>(inject('reload'));
const locale = computed(() => useLangsStore().locale);
const userInfo = computed(() => useUserInfoStore().userInfo as Record<string, unknown>);
const langList = ref(langConfig.code);
const activeLang = computed(() => {
  return langList.value.find(item => item.code === locale.value)?.name;
});
useUserInfoStore().getUserInfo();
const changeLang = (lang: string) => {
  if (lang === 'en') {
    useEnumStore().getEnumList(lang);
  } else {
    useEnumStore().getEnumList();
  }
  useLangsStore().setLocale(lang);
  reload.value.reload();
};
if (localStorage.getItem('langs')) {
  useLangsStore().setLocale(JSON.parse(localStorage.getItem('langs') as string).locale);
}
const logOut = async () => {
  try {
    const resp = await online({
      status: 2,
      account: userInfo.value.username,
    });
    useUserInfoStore().setCsStatus(resp.is_login);
    useAppStore().logOut();
  } catch (error) {
    console.log(error);
  }
};
const isfull = ref(false);
const toggleFullScreen = () => {
  const el = document.documentElement as any;
  screenfull.toggle(el);
  isfull.value = !isfull.value;
};
</script>

<style lang="scss" scoped>
.header-box {
  height: 100%;
  user-select: none;
  .logo {
    width: 115px;
    height: 36px;
    background-image: url('/src/assets/img/logologo.png');
    background-repeat: no-repeat;
    background-size: contain;
    background-position: center;
    float: left;
    margin-top: 5px;
    margin-left: 11px;
    cursor: pointer;
  }
  .line {
    height: 24px;
    width: 0;
    border-right: 1px solid rgba(255, 255, 255, 0.3);
    margin: 12px 18px 0px;
    float: left;
  }
  .sys-name {
    color: #ffffff;
    font-size: 16px;
    font-family: PingFangSC-Regular;
    font-weight: 400;
    white-space: nowrap;
    display: block;
    cursor: pointer;
    height: 100%;
    line-height: 100%;
    float: left;
    line-height: 48px;
    width: 210px;
    color: #fff;
  }
  .full-screen {
    float: left;
    margin: 14px 0px 0px 0px;
    cursor: pointer;
    &:deep(.iconfont) {
      transition: all 0.3s;
      font-size: 18px;
      color: rgba(255, 255, 255, 0.75);
    }
  }
  .full-screen:hover {
    &:deep(.iconfont) {
      color: rgba(255, 255, 255, 1);
    }
  }
  .user-info {
    float: left;
    margin-top: 13px;
  }
  .user {
    cursor: pointer;
    color: rgba(255, 255, 255, 0.75);
    font-size: 20px;
    .el-icon {
      margin-top: 1px;
    }
    &:deep(i) {
      transition: all 0.3s;
    }
  }
  .user:hover {
    color: rgba(255, 255, 255, 1);
    // &:deep(i) {
    //   transform: rotate(180deg);
    // }
  }
  .lang-info {
    float: left;
  }
  .lang {
    margin-top: 15px;
    float: left;
    color: rgba(255, 255, 255, 0.75);
    text-align: center;
    cursor: pointer;
    &:deep(i) {
      transition: all 0.3s;
    }
  }
  .lang:hover {
    color: rgba(255, 255, 255, 1);
    &:deep(i) {
      transform: rotate(180deg);
    }
  }
  .handle-box {
    font-family: PingFangSC-Regular;
    height: 100%;
    float: right;
  }
}
</style>
