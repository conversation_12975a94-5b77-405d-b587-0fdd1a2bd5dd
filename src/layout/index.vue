<template>
  <el-container class="main-container">
    <el-header class="main-header">
      <opsHeader />
    </el-header>
    <el-container v-if="isRouterAlive" :class="[collapseMark ? 'mic-position' : '', 'main-body']">
      <el-aside class="main-sidebar">
        <opsSide />
      </el-aside>
      <el-main class="main-view">
        <el-config-provider :locale="locale === 'zh' ? elZhCn : elEn">
          <opsNav class="ops-nav" />
          <div class="router-view-wapper">
            <el-watermark
              :font="font"
              :content="userInfo.username as string"
              style="height: 100%; width: 100%"
            >
              <router-view :key="key" />
            </el-watermark>
          </div>
        </el-config-provider>
      </el-main>
    </el-container>
  </el-container>
</template>

<script lang="ts">
import elZhCn from 'element-plus/es/locale/lang/zh-cn';
import elEn from 'element-plus/es/locale/lang/en';
import { useLangsStore, useAppStore, useUserInfoStore, useEnumStore } from '@/stores';
import opsHeader from './components/opsHeader.vue';
import opsSide from './components/opsSider.vue';
import opsNav from './components/opsNav.vue';
export default defineComponent({
  name: 'Home',
  components: {
    opsHeader,
    opsSide,
    opsNav,
  },
});
</script>
<script setup lang="ts">
const locale = computed(() => useLangsStore().locale);
const isRouterAlive = ref(true);
const route = useRoute();
const key = computed(() => route.fullPath);
const collapseMark = computed(() => useAppStore().collapseMark);
const userInfo = computed(() => useUserInfoStore().userInfo as Record<string, unknown>);
useEnumStore().getGameList(); // 获取游戏列表
useEnumStore().getLangsList(); // 获取语言列表
useEnumStore().getEnumList(); // 获取枚举列表
const font = ref({
  fontSize: 16,
  color: 'rgba(0, 0, 0, 0.06)',
});
const reload = () => {
  isRouterAlive.value = false;
  nextTick(() => {
    isRouterAlive.value = true;
  });
};
provide('reload', {
  reload,
});
</script>

<style lang="scss" scoped>
.main-sidebar {
  transition: width 0.28s;
  width: 230px !important;
  background-color: #f7f7f7;
  border-right: 1px solid #dadfe3;
  box-shadow: -3px 6px 10px -1px rgba(0, 0, 0, 0.6);
  position: fixed;
  top: 48px;
  bottom: 0px;
  left: 0;
  z-index: 1001;
  overflow: hidden;
  &:deep(.el-menu) {
    background-color: #f7f7f7;
  }
}
.main-view {
  min-height: 100%;
  transition: margin-left 0.28s;
  margin-left: 230px;
  position: relative;
  padding: 0;
  .ops-nav {
    height: 40px;
    border-bottom: 1px solid #e4e9ed;
    box-sizing: border-box;
    padding: 0 20px;
  }
  .router-view-wapper {
    height: calc(100% - 40px);
    background: #fff;
  }
}
.mic-position {
  .main-view {
    margin-left: 64px;
  }
  .main-sidebar {
    width: 64px !important;
  }
}
</style>
