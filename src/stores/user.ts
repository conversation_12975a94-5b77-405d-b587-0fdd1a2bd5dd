/*
 * @Author: wenh<PERSON>.wang
 * @Date: 2024-04-13 18:19:29
 * @Last Modified by: wenhao.wang
 * @Last Modified time: 2024-05-14 16:57:30
 */

import { defineStore } from 'pinia';
import { ref } from 'vue';
import axios from 'axios';
import { type RouteRecordRaw } from 'vue-router';
import router from '@/router';
import { ElMessage } from 'element-plus';
import { recursionRouter } from '@/utils/permissionFilter';
import { useAppStore } from '@/stores/app';

import projectRouters from '@/router/projectRouters';

export const useUserInfoStore = defineStore('user', () => {
  const userTocken = ref('');
  const routes = ref<RouteRecordRaw[]>([]);
  const permissions = ref<{
    routers: Record<string, unknown>[];
    button: Record<string, unknown>[];
    game_list: string[];
  }>({
    routers: [],
    button: [],
    game_list: [],
  });
  const userInfo = ref<Record<string, unknown>>({});
  const isLogin = ref(2);
  async function setUserTocken(token: string) {
    userTocken.value = token;
    if (routes.value.length > 0) {
      return;
    }
    try {
      // 请求用户权限路由
      const resp = await axios({
        method: 'post',
        url: '/gateway/api/per/views',
        headers: {
          'admin-gateway-token': userTocken.value,
        },
        data: {
          prd_code: 'ops-ticket-api',
        },
      });
      const { code, data } = resp.data;
      if (code !== 0 || !data.routers || data.routers.length === 0) {
        ElMessage.error('获取权限失败或无权限,请联系管理员！');
        const appStore = useAppStore();
        appStore.logOut();
        return;
      }
      permissions.value = data;
      const perRoutersList = data.routers.map((i: Record<string, unknown>) => i.code);
      const realRouter = recursionRouter(perRoutersList, projectRouters);
      // const realRouter = projectRouters // 无权限控制
      routes.value = realRouter;
      const finalRoutes = [...router.options.routes, ...realRouter];
      finalRoutes.forEach(route => router.addRoute(route));

      const lastPath = sessionStorage.getItem('path');
      router.push(lastPath || realRouter[0].path);
    } catch (error) {
      ElMessage.error('获取用户权限失败：' + error);
    }
  }

  function setCsStatus(status: number) {
    isLogin.value = status;
  }
  async function getUserInfo() {
    const resp = await axios({
      method: 'get',
      url: '/gateway/api/userInfo',
      headers: {
        'admin-gateway-token': userTocken.value,
      },
    });
    const { code, data } = resp.data;
    if (code !== 0) {
      ElMessage.error('获取用户信息失败!');
      return;
    }
    userInfo.value = data;
    const csStatusResp = await axios({
      method: 'post',
      url: '/gateway/proxy/ops-ticket-api/all/api/ticket/is_acceptor',
      headers: {
        'admin-gateway-token': userTocken.value,
      },
      data: {
        account: userInfo.value.username,
      },
    });
    const { code: csCode, data: csData } = csStatusResp.data;
    if (csCode === 0) {
      setCsStatus(csData.is_login);
    } else {
      ElMessage.error('获取客服状态失败！');
    }
  }

  return {
    userTocken,
    routes,
    permissions,
    setUserTocken,
    getUserInfo,
    userInfo,
    isLogin,
    setCsStatus,
  };
});

export const useEnumStore = defineStore(
  'enum',
  () => {
    // 获取游戏列表
    const gameList = ref<
      Record<
        string,
        {
          app_name: string;
          game_project: string;
        }
      >
    >({});
    async function getGameList() {
      try {
        const resp = await axios({
          method: 'post',
          url: `/gateway/proxy/ops-ticket-api/all/api/addons/game_list`,
          headers: {
            'admin-gateway-token': useUserInfoStore().userTocken,
          },
        });
        const { code, data, msg } = resp.data;
        if (code === 0) {
          Object.keys(data)
            .sort((a, b) => {
              return data[a].order - data[b].order;
            })
            .forEach(key => {
              gameList.value[key] = data[key];
            });
        } else {
          console.log('获取游戏列表失败：', resp);
          if (code === 2000) {
            const appStore = useAppStore();
            appStore.logOut();
          }
          ElMessage.error('获取用户游戏列表失败：' + msg);
        }
      } catch (error) {
        ElMessage.error('获取用户游戏列表失败：' + error);
      }
    }

    // 强制更新游戏列表数据
    async function forceUpdateGameList() {
      try {
        // 清空当前数据
        gameList.value = {};

        const resp = await axios({
          method: 'post',
          url: `/gateway/proxy/ops-ticket-api/all/api/addons/game_list`,
          headers: {
            'admin-gateway-token': useUserInfoStore().userTocken,
          },
          // 添加时间戳参数避免缓存
          params: {
            _t: new Date().getTime(),
          },
        });
        const { code, data, msg } = resp.data;
        if (code === 0) {
          Object.keys(data)
            .sort((a, b) => {
              return data[a].order - data[b].order;
            })
            .forEach(key => {
              gameList.value[key] = data[key];
            });
          return true;
        } else {
          console.log('强制更新游戏列表失败：', resp);
          if (code === 2000) {
            const appStore = useAppStore();
            appStore.logOut();
          }
          ElMessage.error('强制更新游戏列表失败：' + msg);
          return false;
        }
      } catch (error) {
        ElMessage.error('强制更新游戏列表失败：' + error);
        return false;
      }
    }

    // 获取语言列表
    const LangsList = ref<
      {
        code: string;
        name: string;
      }[]
    >([]);
    async function getLangsList() {
      try {
        const resp = await axios({
          method: 'post',
          url: `/gateway/proxy/ops-ticket-api/all/api/addons/lang`,
          headers: {
            'admin-gateway-token': useUserInfoStore().userTocken,
          },
        });
        const { code, data, msg } = resp.data;
        if (code === 0) {
          LangsList.value = data;
        } else {
          console.log('获取语言列表失败：', resp);
          if (code === 2000) {
            const appStore = useAppStore();
            appStore.logOut();
          }
          ElMessage.error('获取语言列表失败：' + msg);
        }
      } catch (error) {
        ElMessage.error('获取语言列表失败：' + error);
      }
    }

    // 获取系统枚举
    const enumList = ref<Record<string, any>>({});
    async function getEnumList(lang?: string) {
      try {
        const resp = await axios({
          method: 'post',
          url: `/gateway/proxy/ops-ticket-api/all/api/addons/enum`,
          headers: {
            'admin-gateway-token': useUserInfoStore().userTocken,
            lang,
          },
        });
        const { code, data, msg } = resp.data;
        if (code === 0) {
          enumList.value = data;
        } else {
          console.log('获取枚举列表失败：', resp);
          if (code === 2000) {
            const appStore = useAppStore();
            appStore.logOut();
          }
          ElMessage.error('获取枚举列表失败：' + msg);
        }
      } catch (error) {
        ElMessage.error('获取枚举列表失败：' + error);
      }
    }

    return {
      gameList,
      LangsList,
      enumList,
      getGameList,
      getLangsList,
      getEnumList,
      forceUpdateGameList,
    };
  },
  {
    persist: {
      storage: window.sessionStorage,
    },
  }
);
