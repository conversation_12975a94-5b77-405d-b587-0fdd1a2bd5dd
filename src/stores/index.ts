/*
 * @Author: wenh<PERSON>.wang
 * @Date: 2024-04-13 18:18:54
 * @Last Modified by:   wenhao.wang
 * @Last Modified time: 2024-04-13 18:18:54
 */

import { createPinia } from 'pinia';
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate';

const store = createPinia();
store.use(piniaPluginPersistedstate);

export default store;

export * from './user';
export * from './app';
export * from './langs';
