/*
 * @Author: wenh<PERSON>.wang
 * @Date: 2024-04-13 18:19:04
 * @Last Modified by: wenhao.wang
 * @Last Modified time: 2025-02-17 19:50:57
 */

import { defineStore } from 'pinia';
import { ref } from 'vue';

export const useAppStore = defineStore(
  'app',
  () => {
    // 下载进度
    const progressNum = ref<number>(-1);
    const startTime = ref<number>(0);
    const Progress = (progress: number) => {
      progressNum.value = progress;
    };
    const setStartTime = (time: number) => {
      startTime.value = time;
    };

    // 从 localStorage 获取初始状态，如果没有则默认为 true
    const collapseMark = ref(localStorage.getItem('sidebarStatus') !== 'false');

    function logOut() {
      localStorage.clear();
      sessionStorage.clear();
      location.href =
        `/gateway/auth/logout?referer=` + encodeURIComponent(`${location.origin}/login`);
    }

    function toggleSideBar() {
      collapseMark.value = !collapseMark.value;
      // 保存状态到 localStorage
      localStorage.setItem('sidebarStatus', collapseMark.value.toString());
    }

    return { logOut, toggleSideBar, Progress, progressNum, setStartTime, startTime, collapseMark };
  },
  {
    persist: true, // 启用 pinia 持久化
  }
);
