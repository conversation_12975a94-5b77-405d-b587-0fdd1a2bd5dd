import FpRequest from '../server';
import config from './config';
const API = `${config.BASE_API}`;

// 调查问卷配置列表
export const questionnaireConfigList: ApiT = params =>
  FpRequest.post(`${API}/survey_config/list`, params);
// 问卷配置禁用启用
export const questionnaireConfigEnable: ApiT = params =>
  FpRequest.post(`${API}/survey_config/enable`, params);
// 生成链接
export const questionnaireConfigCreateUrl: ApiT = params =>
  FpRequest.post(`${API}/survey_config/gen_links`, params);
// 获取问卷配置详情
export const questionnaireConfigDetail: ApiT = params =>
  FpRequest.post(`${API}/survey_config/info`, params);
// 问卷配置保存
export const questionnaireConfigSave: ApiT = params =>
  FpRequest.post(`${API}/survey_config/edit`, params);
