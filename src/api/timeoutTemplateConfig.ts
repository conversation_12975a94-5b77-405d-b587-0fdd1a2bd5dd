import FpRequest from '../server';
import config from './config';
const API = `${config.BASE_API}`;

// 模板列表
export const tempLibList: ApiT = params => FpRequest.post(`${API}/overtime_tpl/list`, params);
// 模板禁用启用
export const editEnableTempLib: ApiT = params =>
  FpRequest.post(`${API}/overtime_tpl/enable`, params);
// 模板删除
export const delTempLib: ApiT = params => FpRequest.post(`${API}/overtime_tpl/delete`, params);
// 模板新增
export const addTempLib: ApiT = params => FpRequest.post(`${API}/overtime_tpl/add`, params);
// 模板编辑
export const editTempLib: ApiT = params => FpRequest.post(`${API}/overtime_tpl/edit`, params);
