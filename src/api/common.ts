import FpRequest from '../server';
import config from './config';
const API = `${config.BASE_API}`;

// 客服上下线接口
export const online: ApiT = params => FpRequest.post(`${API}/ticket/is_acceptor`, params);
// 上传图片接口（老工单）
export const uploadImage: ApiT = params =>
  FpRequest.post(`/gateway/proxy/cs_ticket/all/api/addons/upload`, params);
// CDN静态文件下载
export const downloadFile: ApiT = params =>
  FpRequest.download(params.url as string, 'get', params, true);
// 获取质检消息列表
export const getQualityMessageList: ApiT = params =>
  FpRequest.post(`${API}/examine/order/notice/list`, params);
// 质检消息红点
export const getQualityMessageRedDot: ApiT = params =>
  FpRequest.post(`${API}/examine/order/notice/acceptor`, params);
