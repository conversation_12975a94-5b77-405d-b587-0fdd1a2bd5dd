import FpRequest from '../server';
import config from './config';
const API = `${config.BASE_API}`;

// 策略列表
export const strategyList: ApiT = params => FpRequest.post(`${API}/v2/strategy/list`, params);

// 启用/禁用策略
export const strategyEnable: ApiT = params => FpRequest.post(`${API}/v2/strategy/enable`, params);

// 删除策略
export const strategyDelete: ApiT = params => FpRequest.post(`${API}/v2/strategy/del`, params);

// 保存策略
export const strategySave: ApiT = params => FpRequest.post(`${API}/v2/strategy/save`, params);
