{"title": "SWIFT", "menu_tickets_overview": "Tickets Overview", "menu_overview": "Overview", "menu_sys_config": "System Config", "menu_sys_issue_config": "Issue Category Config", "menu_sys_template_config": "Templage Config", "menu_sys_manage_templage": "Manage Templage", "menu_sys_tag_config": "Tag Config", "menu_sys_skills_config": "Skill Group Config", "menu_first_line": "First Tier Workstation", "menu_second_line": "Second Tier Workstation", "menu_third_line": "Third Tier Workstation", "menu_vip_line": "VIP Workstation", "menu_first_pool": "First Tier Ticket Pool", "menu_second_pool": "Second Tier Ticket Pool", "menu_third_pool": "Third Tier Ticket Pool", "menu_vip_pool": "VIP Ticket Pool", "menu_my_pool": "My Tickets", "text_create_time": "Created at", "text_end_time": "End Time", "text_processed_by": "Processed by", "text_processed_by_placeholder": "Please fill in the processor account", "text_current_stage": "Current Stage", "text_current_stage_placeholder": "Please Select Current Stage", "text_issue_category": "Issue Category", "text_ticket_lang": "Ticket Language", "text_ticket_lang_placeholder": "Please Select Ticket Language", "text_submitted_by": "Submitted by", "text_ticket_by": "Ticket Source", "text_ticket_by_placeholder": "Please Select Ticket Source", "text_urgency_level": "Level of Urgency", "text_urgency_level_placeholder": "Please Select Level of Urgency", "text_ticket_tag": "Ticket Tags", "text_tag_placeholder": "Please Select Tags", "project": "Project", "text_project_placeholder": "Please Select Project", "text_ticket_num": "Ticket Number", "text_ticket_num_placeholder": "Please Input Ticket Number", "text_ticket_status": "Ticket Status", "text_ticket_status_placeholder": "Please Select Ticket Status", "text_vip_level": "VIP Level", "text_vip_level_placeholder": "Please Select VIP Level", "btn_search": "Search", "btn_reset": "Reset", "btn_ticket_export": "Export Ticket", "text_details": "Details", "text_assign": "Assign", "text_player_nickname": "Player Nickname", "text_history": "History", "text_form_info": "Form Info", "text_replenish_info": "Replenish Info", "text_ticket_process": "Ticket Processing", "text_solution": "Solutions", "text_view_log": "View Log", "text_ticket_log": "Ticket Log", "btn_cancel": "Cancel", "btn_ok": "OK", "text_pending_ticket": "In Processing", "text_ticket_processed": "Tickets Processed", "text_add_info": "Add Info", "text_tag": "Tags", "text_disable": "Disable", "text_enable": "Enable", "text_user_info": "User Info", "text_recharge_amount": "Recharge Amount", "text_player_language": "Player Language", "text_server_id": "Server ID", "text_game_version": "Game Version", "text_history_record": "History Record", "btn_add_remark": "Add note information", "btn_edit": "Edit", "btn_add_tag": "Add Tag", "text_tag_name": "Tag Name", "text_tag_desc": "Tag Description", "text_update_at": "Changed at", "text_update_by": "Changed by", "text_status": "Status", "text_pending_status": "Pending", "text_precessed_status": "Processed", "text_finish_status": "Finish", "btn_op": "Action", "text_fpid": "Player Fpid", "cs_account": "Customer Service Account", "start_date": "Start Date", "end_date": "End Date", "to": "to", "btn_batch_operation": "Batch Operation", "btn_batch_mark": "<PERSON><PERSON>", "place_select": "Please Select", "place_input": "Please Input", "search_type": "Type", "dialog_title_assign_cs": "Assign customer service", "assign_cs_placeholder": "Select the person to be assigned", "res_assign_failed": "Assignment failed", "res_batch_op_v": "The work order you select cannot be batch operated in the first-line work order pool!", "res_batch_op_diff": "The data you want to operate in batches is not the same transfer node, please select the data of the same transfer node!", "res_batch_op_not_in_second": "The work order you selected cannot be batch operated in the second-line work order pool!", "res_update_urgency_failed": "Failed to change urgency", "high": "High", "mid": "Middle", "low": "Low", "btn_create_ticket": "Create Ticket", "btn_receive_ticket": "Receive Ticket", "res_receive_ticket_success": "Receive Ticket Success", "btn_reply_user": "Reply to user", "btn_fill": "Fill in", "info_no_data": "Sorry, No Data", "text_history_chat": "Historical chat", "text_no_history_chat": "No chat history", "text_perform_operate": "Perform operation", "text_reply_msg": "Reply message", "text_remark_tip": "Note: The added remarks will be displayed in the log", "text_transfer_object": "Transfer object", "text_player": "Player", "text_first_line_cs": "First-line agent", "text_second_line_cs": "Second-line agent", "text_third_line_cs": "Third-line agent", "text_vip_cs": "VIP agent", "text_transfer_op": "Circulation operation", "text_audit_area": "Audit area", "text_audit_area_third": "Audit area(Third Tier)", "text_reply_content": "Reply content", "text_success": "Success", "text_error": "Error", "text_place_select_date_time": "Please select a date and time", "res_img_limit20": "The total number of uploaded pictures exceeds 20", "text_ticket_refill": "Ticket Refill", "text_callback_operator": "Call back the operator", "text_callback_time": "Call back time", "text_remark_info": "Remark Info", "text_edit": "Edit", "text_delete": "Delete", "text_all": "All", "text_device_type": "Device Model", "text_os_version": "OS Version", "text_rom_gb": "OS ROM", "text_remain_rom": "OS ramin ROM", "menu_ticket_report": "Ticket Report", "menu_tag_report": "Tag Report", "menu_staff_report": "Staff Report", "text_logout": "Logout", "text_time_range": "Time Range", "text_result_total": "A total of {total} query results", "text_time": "Time at", "text_hits": "Hits", "text_solved_quantity": "Solved quantity", "text_first_response_time": "Time to ticket received", "text_process_time": "Processing time", "text_one_time_process_rate": "One-time processing rate", "text_one_time_resolution_rate": "One-time resolution rate", "text_24hour_resolution_rate": "Completion rate in 24hr", "text_solved_24h": "Completed tickets in 24hr", "text_72hour_resolution_rate": "Completion rate in 72hr", "text_solved_72h": "Completed tickets in 72hr", "text_appraise_rate": "Evaluation rate", "text_appraise": "Number of evaluations", "text_satisfaction_rate": "Satisfaction rate", "text_satisfaction": "Satisfied quantity", "text_avg_score": "Average star rating", "text_ticket_under_review": "Work order under review", "btn_download": "Download", "text_avg_response_time": "Avg time to ticket received", "text_avg_solve_time": "Average processing time", "text_ticket_catetory": "Ticket Category", "text_new_increment": "New increment", "text_cancel": "Cancel", "text_confirm": "Confirm", "know_m_rich_placeholder": "Please input", "text_cs": "Customer Service", "text_skill_group": "Skill group", "text_cs_create_ticket": "Number of ticket submitted on behalf of users", "text_num_of_receive_tickets": "Number of tickets received", "text_num_of_callback": "Number of calls back", "text_upgrade_rate": "Upgrade rate", "text_operator": "Operator", "text_fill_source": "Fill source", "text_fill_content": "Fill comtent", "text_fill_file": "Supplementary documents", "text_img_preview": "Picture Preview", "text_prompt": "Prompt", "text_assign_ticket": "Are you sure to assign the ticket to {name} ?", "text_form_content": "Form Info", "text_user_comments": "Customer Comment", "text_csi_label": "Rating Tags", "text_rating_star": "Star Rating", "text_service_time": "Avg Handling Time by agents", "text_third_solved": "Number of Solved Tickets by Third Tier", "text_second_solved": "Number of Solved Tickets by Second Tier", "text_holding_time": "Avg Holding Time", "text_existing_tag": "Tagged", "text_ticket": "Ticket", "btn_preview": "Preview", "text_complete_state": "Refill Status", "text_country": "Country", "text_high_operate": "High-Level", "text_left_smaller": "The value on the left of Server should be less than the value on the right", "text_server": "Server", "text_first_response": "Avg time to ticket received from first-tier workstation", "text_second_response": "Avg time to ticket received from second-tier workstation", "text_audit_response_third": "Avg time to ticket received from Audit Area(Third Tier)", "text_audit_response": "Avg time to ticket received from Audit Area", "text_first_solve": "Avg time to resolve of First-Tier Workstation", "text_second_solve": "Avg time to resolve of Second-Tier Workstation", "text_third_solve": "Avg time to resolve of Third-Tier Workstation", "text_third_response": "Avg time to ticket received from Third-Tier workstation", "text_audit_solve": "Avg time to resolve of Audit area", "text_audit_solve_third": "Avg time to resolve of Audit area(Third Tier)", "text_first_one_time_process_rate": "One-time resolve rate (First Tier)", "text_second_one_time_process_rate": "One-time resolve rate (Second Tier)", "text_callback_refill_rate": "Refill Rate", "text_refill_success_rate": "Success rate of Refill", "text_first_trans_rate": "Transfer rate of First-Tier Workstation", "text_second_trans_rate": "Transfer rate of Second-Tier Workstation", "text_transfer_times": "Transfer Times", "text_transfer_num": "Transfer Tickets", "text_refill_times": "Refill Times", "text_refill_rate": "Refill Rate", "text_be_refill_times": "Times of agents were asked to refill", "text_be_refill_num": "Ticket number of agents were asked to refill", "text_be_refill_rate": "Rate of agents were asked to refill", "text_transfer_rate": "Transfer Rate", "text_csi_info": "Rating Info", "text_csi_num": "Rating Score", "text_csi_content": "Comment", "text_channel": "Pkg Channel", "btn_delete_mark": "Batch delete tags", "text_same_tags": "Added common tags", "text_select_all": "Select All", "text_null": "Blank", "text_current_op": "Current operator", "btn_process": "Process", "menu_tag_lib": "Tag Base Configuration", "text_tag_lib": "Tag Base", "text_percent": "Proportion", "res_tag_op": "Please select data within the same project!", "text_include": "Contains", "text_not_include": "Contains none of", "text_system_tag": "System Tags", "text_history_tag": "History Tags", "text_timeout_ticket": "Time-out tickets", "text_tag_type": "Tag Type", "btn_add_tag_base": "Add Tag Base", "text_tag_lib_id": "Tag Base ID", "text_tag_lib_name": "Tag Base Name", "text_tag_lib_desc": "Tag Base Description", "text_linked_game": "Linked Games", "text_linked_agents": "Linked Agents", "text_changed_by": "Changed By", "text_configure": "Configure", "text_status_success": "Status changed successfully", "btn_edit_tag_base": "Edit Tag Base", "text_add_success": "Successfully added", "text_edit_success": "Successfully edited", "text_edit_linked_agents": "Edit linked agents", "text_user_not_exist": "User{user} not exist", "text_link_success": "Successfully linked", "btn_first_level_tag": "Add First-Tier tag", "btn_same_level": "Same Tier", "btn_next_level": "Subordinate", "text_edit_tag": "Edit Tag", "text_add_tag": "Add Tag", "menu_scene_config": "Entrance Configuration", "text_is_evaluation": "Rate or not", "text_yes": "Yes", "text_no": "No", "text_scene_type": "Entrance Type", "text_loading_type": "Loading", "text_banned_type": "Account Ban", "text_in_game_type": "In-game", "menu_team_config": "Team Configuration", "text_team": "Team", "btn_close": "Close", "text_none": "None", "btn_release": "SEND", "text_release_confirm": "SEND CONFIRM", "text_release_success": "SEND SUCCESS", "text_push_ticket": "Push Form Tickets", "btn_batch_assign": "RAPID ASSIGN", "text_select_not_done": "FAILED: Cannot apply rapid switch to completed tickets", "text_account_id": "AccountId", "res_batch_op_not_in_thd": "Sorry, tickets at stage of 'Ticket Refill', 'Ticket Closed' and 'Resolve Ticket' cannot be batched", "text_base64_tips": "The image in your pasted content does not support direct Copy & Paste. You may right click on the image, then copy the image first before pasting. Alternatively, you can download the image and manually upload it via Upload Image in the text box.", "text_first_manager": "Frontline Administrator", "menu_front_manager_workbench": "Frontline Administration Workstation", "menu_front_manager_pool": "Frontline Administration Ticket Pool", "res_batch_op_not_in_front_manager": "The ticket(s) you selected cannot be operated in batches in Frontline Administration Ticket Pool", "res_batch_op_not_in_third_pool": "The ticket(s) you selected cannot be operated in batches in Third Tier Ticket Pool", "btn_batch_reply_user": "Reply temporarily in batches", "btn_batch_tickets_close": "Close Ticket in batches", "text_batch_close_confirm": "Are you sure to close ticket:{id} ?", "text_user_uid": "UID", "text_user_dimensionality": "Dimensions of Personnel", "text_user_value": "Personnel", "text_staff": "Staff", "menu_system_op": "System operation", "menu_ticket_withdraw": "Ticket retraction", "menu_field_map": "Quote fields to map configuration", "text_first_manager_response": "Avg time to ticket received from Frontline Administration Workstation", "text_first_manager_solve": "Avg time to resolve of Frontline Administration Workstation", "text_first_manager_solved": "Number of Solved Tickets by Frontline Administrator", "text_upgrade_time": "Escalation time", "text_inner_upgrade_time": "Internal escalation time", "text_first_manager_one_time_process_rate": "One-time resolve rate (Frontline Administration)", "text_third_one_time_process_rate": "One-time resolve rate (Third Tier)", "text_first_manager_trans_rate": "Transfer rate of Frontline Administration", "text_third_trans_rate": "Transfer rate of Third Tier", "menu_sys_reply_template_config": "Template Config", "text_system": "System", "text_ticket_service_role": "Manual handling ticket", "text_sub_channel": "Special Channel Bundle", "menu_automation": "Automated Process", "menu_automation_flowConfig": "Process Configuration", "menu_automation_componentConfig": "Component Configuration", "menu_automation_interfaceWrapConfig": "API Encapsulation Configuration", "menu_automation_interfaceConfig": "API Configuration", "text_auto_log": "Automated Log", "btn_batch_remark": "Batch Note", "text_auto_history_record": "Automated Process Message", "text_created_at": "Operation Time", "text_operate": "Operator", "text_op_detail": "Operation Details", "reply_to_player": "Reply Player", "internal_circulation": "Internal Circulation", "preview_messages": "Preview Message", "project_name_configuration": "Project Name Configuration", "menu_top_info_config": "Top Information Configuration", "text_vip_processing": "VIP Processing", "text_vip_filling": "VIP Filling", "text_vip_process_completed": "VIP Process Cpmplete", "text_vip_customer_submit": "VIP Submit", "text_vip_filling_node": "VIP Refilled", "text_vip_customer": "VIP Customer Service", "text_callback_filling": "Return to Refill", "text_reopen_workorder": "Ticket Reopen", "text_player_submit": "Player Submission", "text_internal_circulation": "Internal Submission", "text_vip_new": "VIP-new", "text_reopen_new_changes": "Reopening Ticket Increment", "text_reopen_solved_quantity": "Resolved Reopening Ticket Volume", "text_reopen_info": "Reopening Description", "text_reopen_nums": "Reopen Count", "text_reopen_time": "Time to Reopen", "first_time_solution": "First Time Resolution", "one_time_solution": "First Reopened Resolution", "two_time_solution": "Second Reopened Resolution", "three_time_solution": "Third Reopened Resolution", "four_time_solution": "Fourth Reopened Resolution", "five_time_solution": "Fifth Reopened Resolution", "text_appraise_attitude": "Service Attitude Rating", "text_appraise_speed": "Processing Speed Rating", "text_appraise_treatment": "Processing Method Rating", "text_completion_quantity": "Closed Tickets", "text_reopen_quantity": "Reopened Tickets", "text_reopen_rate": "Reopen Rate", "text_unresolved_quantity": "Unresolved Tickets", "text_average_reopens": "Times Tickets Reopened (Avg.)", "text_reopen_interval_time": "Reopen Interval", "text_completed_quantity": "Reopened Tickets Closed", "text_resolution_rate": "Reopened Ticket Resolution Rate", "text_all_resolution_rate": "Overall Resolution Rate", "first_process_time": "Initial Processing Time", "customer_process_time": "Customer Support Initial Processing Time", "text_online": "Online", "text_offline": "Offline", "menu_homepage": "Homepage", "menu_sys_assign": "Assign <PERSON>s", "text_pending_ticket_my": "My Open Tickets", "text_finish_ticket_my": "My Closed Tickets", "text_pending_ticket_zh": "Open Tickets-zh", "text_pending_ticket_vip": "Open Tickets-vip", "text_ticket_upgraded": "Pending Tickets", "text_full_screen": "Click to Fullscreen", "text_close_full_screen": "Exit Fullscreen", "text_add_tag_lib": "Add Tag", "text_add_config": "Add", "text_edit_config": "Edit", "text_online_success": "Online Success", "text_offline_success": "Offline Success", "text_game": "Game", "text_lang": "Language", "text_upper_limit": "Limit", "text_del_success": "Delete Success", "text_group_id": "Team ID", "text_ticket_total": "Total", "text_evaluation_time": "Comment Time", "text_sid_place": "Server No.,eg: 308", "text_qtype": "Ticket Type", "text_creator_type": "Nickname", "text_acceptor_type": "Assignee", "text_content": "Content", "text_acc_amount": "Recharge", "text_pending_time": "Waiting Time", "text_conversation": "Conversations", "text_base_info": "Meta Data", "text_history_ticket": "History Tickets", "text_suborder_log": "Ticket Log", "text_total_tip": "Total", "text_sort": "Sort", "text_is_upgrade": "Pending Tickets", "text_tag_temp": "Tag Template", "btn_upgrade": "Escalate", "btn_downgrade": "De-escalate", "btn_transfer": "Assign", "btn_reply": "Reply", "btn_reply_close": "Reply&Resolve", "btn_refuse": "Reject", "text_data_overview": "Data Overview", "text_mult_data_overview": "Multi Data", "text_input_relevant_info": "Please enter relevant information", "text_cannot_empty": "Reply message cannot be empty!", "text_template_name": "Template Name", "text_template_content": "Template Content", "text_new_add_template": "New Add Template", "text_batch_add": "<PERSON><PERSON> Add", "text_template_id": "Template ID", "text_add_template": "Add Template", "text_edit_template": "Edit Template", "text_quote_template": "QuoteTemplate", "text_score_and_evaluate_info": "Score And Evaluate message", "text_service_feel": "Service Fell", "text_nps": "NPS", "text_other_explain": "Other Explain", "menu_chatting": "Chatting", "menu_discord": "Discord", "menu_sys_relationship": "Player Relationship Config", "text_player_dc_id": "Player DC ID", "text_player_dc_name": "Player DC Name", "text_player_dc_account": "Player DC account", "text_correlation_player_fpid": "Correlation player fpid", "text_correlation_commissioner": "Correlation commissioner", "text_new_add": "New Add", "text_data_export": "Data Export", "text_correlation_fpid": "Correlation fpid", "text_vip_commissioner": "VIP Commissioner", "text_delete_sure": "Delete Sure", "text_player_content": "Please enter the player's input information", "text_remark_content": "Please enter a note", "text_player_dc": "Player's DC nickname", "text_player_dcid": "Player's DC ID", "text_dc_pw": "DC account", "text_dc_nick": "DC nickname", "text_total_pay": "Total amount paid", "text_maintainer": "Account manager", "text_online_chat": "Online chat", "text_user_portrait": "Portrait information", "text_send_to": "Send to", "text_loading_more": "Pull down to load more", "text_loading_tip": "Loading desperately...", "text_loading_success": "Load successful!", "text_sending": "Sending...", "text_click_show_tip": "Click to view the content of this quoted conversation.", "text_has_edit": "Edited", "text_have_unread": "You have unread messages", "text_has_delete": "This message has been deleted", "text_all_vip_account": "All VIP accounts", "text_pending_reply_message_account": "Accounts awaiting reply", "text_mine_pending_reply_account": "My accounts awaiting reply", "text_no_answer": "Awaiting reply", "text_replied": "Replied", "text_save": "Save", "text_gender": "Gender", "text_birthday": "Birthday", "text_career": "Occupation", "text_education_level": "Educational level", "text_marital_status": "Marital status", "text_ertility_status": "Fertility status", "text_primary_school": "Elementary school", "text_middle_school": "Junior high school", "text_high_school": "High school", "text_undergraduate_course": "Bachelor's degree", "text_postgraduate": "Master's degree", "text_dr": "Doctorate", "text_postdoctor": "Post-doctorate", "text_others": "Other", "text_married": "Married", "text_unmarried": "Single", "text_divorce": "Divorced", "text_lose_wife": "Widowed", "text_unfertilized": "Childless", "text_one_child": "1 child", "text_two_child": "2 children", "text_three_child": "3 children", "text_more_than_one_child": "More than 3 children", "text_recovery_time": "Reply time", "text_boy": "Male", "text_girl": "Female", "text_tag_info": "Tag information", "text_base_attribute": "Basic attributes", "text_unknown": "Unknown", "text_please_enter_tag_content": "Please enter tag content, separate multiple tags with commas", "text_player_sid": "Player server", "text_relations_uid": "Associated UID", "text_relations_sid": "Associated server", "text_msg_noload": "The quoted historical message has not been loaded yet, please scroll to load", "text_cant_read": "This attachment is not supported for parsing, please click to download", "text_unknown_msg_type": "Unknown message type", "btn_not_read": "Unread messages", "menu_sys_template_assign": "Template configuration", "text_search_emoji": "Search emoji", "text_skin_tone": "<PERSON> Tone", "text_peple": "PEOPLE", "text_recent": "FREQUENTLY USED", "text_nature": "NATURE", "text_food": "FOOD", "text_activities": "ACTIVITIES", "text_travel": "TRAVEL", "text_objects": "OBJECTS", "text_symbols": "SYMBOLS", "text_flags": "FLAGS", "text_vip_state": "Vip state", "text_no_vip": "Not vip", "text_last_last_login_time": "Last Login Time", "text_thirty_pay_left_cant_over_right": "The value on the left must not be greater than the value on the right", "text_total_pay_left_cant_over_right": "The value on the left must not be greater than the value on the right", "text_player_tag": "The value on the right cannot be empty", "text_right_data_not_null": "The value on the right cannot be empty  ", "text_left_data_not_null": "The value on the left cannot be empty  ", "menu_report": "Data report  ", "menu_report_interaction": "DC interaction report  ", "menu_report_message": "DC message volume report  ", "text_date": "Date  ", "text_sum": "Total  ", "text_cs_msg_num": "Customer service information count  ", "text_player_msg_num": "Player information count  ", "text_multiple_choice": "Multiple selection  ", "text_create_communication_records": "Create communication record  ", "text_communication_problem": "Communication issue  ", "text_process_status": "Processing status  ", "text_remark": "Remark  ", "text_menu_communication_records": "DC communication record report  ", "text_batch_private_message": "Batch private messages  ", "text_please_batch_message": "Please enter the private message content  ", "text_change_uids": "Selected account UID  ", "text_batch_message_content": "Private message content  ", "text_involving_dialogue": "Involved conversation  ", "text_views": "View  ", "text_edit_communication_records": "Edit communication record  ", "text_select_uid_account_max_fifty": "The selected account UID can be up to 50  ", "send_text": "Send text  ", "send_file": "Send attachment  ", "text_upload_annex": "Upload attachment  ", "text_change_uid_multiple_commas": "Selected account UID, multiple separated by English commas  ", "text_payment_amount": "Cumulative payment amount  ", "text_please_enter_the_text content": "Please enter the text content  ", "text_click_add_mark": "Click to add remarks  ", "text_fixed_tab": "Fixed Tab  ", "text_add_mark_success": "Added remarks successfully  ", "text_please_mark_content": "Please enter the remark content  ", "text_tab_name": "Tab name  ", "text_isnot_all_people_look": "Is it visible to everyone  ", "text_waring_max_ten_tab": "Up to ten tabs can be added  ", "text_please_fixed_tab_search_condition": "Please select the query conditions for the fixed tab first  ", "menu_sys_discord_tag": "DC tag configuration  ", "text_please_tag_describe": "Please enter the tag description  ", "text_please_tag_name": "Please enter the tag name  ", "text_isnot_enable": "Is it enabled  ", "text_please_change_tag": "Please select the tag  ", "menu_sys_autoreply_template_config": "Automatic reply template configuration", "text_reply_template_name": "Reply Template Name", "btn_add_reply_template": "Add reply template", "text_edit_reply_template": "Edit reply template", "text_menu_reply_time_records": "DC reply time report  ", "text_reply_date": "Reply time  ", "text_reply_count": "Number of replies  ", "text_reply_rate": "Proportion  ", "text_esc_key": "ESC key  ", "text_enter_key": "Enter key  ", "text_path_message_single_game": "Batch private messages only support a single game. Please check if the selected items are correct!  ", "text_only_supports_creating_single_game": "Custom tabs only support a single game. Please check if the selected items are correct!  ", "text_batch_del_tags_only_single_game": "Batch tag deletion only supports a single game. Please check if the selected items are correct!  ", "text_batch_mark_only_single_game": "<PERSON><PERSON> remarks only support a single game, please check that the selected item is correct!", "text_patch_tags_only_single_game": "Batch tagging only supports a single game. Please check if the selected items are correct!  ", "text_game_names": "Game nickname  ", "text_processd_by_person": "Most recent handler  ", "text_system_label": "System tags  ", "text_bots": "DC bot  ", "text_return_work_order_pool": "Return to the work order pool  ", "text_current_handlers": "Current handler  ", "text_service_rating": "Service rating  ", "text_team_user": "Team members  ", "text_team_name": "Team name  ", "text_please_input_user": "Please enter a customer service representative  ", "text_please_select_user": "Please select a customer service representative  ", "text_please_input_form_info": "Please enter form information  ", "text_draft_save_success": "Draft saved successfully  ", "text_refresh_success": "Refreshed successfully  ", "btn_ai_summarize": "AI summary  ", "btn_ai_polish": "AI polishing  ", "btn_ai_prerecovery": "AI pre-reply  ", "text_ai_operation": "AI in operation  ", "text_polish_require": "Polishing requirements  ", "btn_polish_start": "Start polishing  ", "btn_confirm_send": "Confirm sending  ", "btn_qa_data": "Summarize Q&A data  ", "text_question": "Question  ", "text_answer": "Answer  ", "text_sync_data": "Synchronize Q&A data to AI Elf  ", "text_remark_messages": "Please enter a remark message  ", "text_patch_message_success": "Batch private messages created successfully  ", "text_please_update_files": "Please check if the file content is correct before uploading!  ", "text_sending_quantity": "Number of messages sent  ", "text_result": "Result  ", "text_founder": "Creator  ", "text_finish_times": "Completion time  ", "text_down_details": "Download details  ", "menu_sys_QCscoringForm_config": "Quality Check Score Form Configuration", "menu_sys_QCtask_config": "Quality Check Task Configuration", "text_scoring_form_config": "Form Name", "text_task_config": "Task Name", "text_QC_form_id": "Score Form ID", "text_QC_form_name": "Score Form Name", "text_title": "Title", "text_module": "<PERSON><PERSON><PERSON>", "text_assessment": "Assessment Item", "text_form_type": "Field Type", "text_option_value": "Please fill in a value", "text_copy": "Copy", "text_ren": "<PERSON><PERSON>", "text_QC_task_id": "Quality Check Task ID", "text_QC_task_name": "Quality Check Task Name", "text_ass_QC_form": "Associated Score Form", "text_sampling_library": "Sampling Database", "text_fail_uids": "Failed UID", "text_all_inspection_standard": "Full Check Criteria", "text_charged_amount": "Total Top-Up Amount ≥", "text_num_search": "Ticket Quantity", "text_sampling_scope": "Sampling Range", "text_part": "Partial", "text_random_check_quantity": "Sampling Quantity", "text_assigned_personnel": "Assignee", "menu_quality_control": "Quality Check", "menu_discord_QC": "Discord Quality Check", "menu_tickets_QC": "Ticket Quality Check", "text_notification_QC": "Quality Check Result Notification", "text_notification_time": "Notification Time", "text_notification_per": "Notification Sender", "text_notification_type": "Notification Type", "text_QC_ID": "Quality Check Order ID", "text_QC_status": "Quality Check Status", "text_QC_result": "Quality Check Result", "text_inspected_person": "Inspectee", "text_quality_inspector": "Inspector", "text_please_change_tag_condition": "Please select tag condition", "text_batch_reply": "<PERSON><PERSON>ly", "text_batch_reply_customs_declaration": "Batch Reply & Close", "text_batch_reject_the_order": "<PERSON>ch Reject", "text_custom_data_overview": "Custom Data Overview", "text_auto_reply_work_orders": "Ticket Auto-Reply", "text_adds": "Add", "text_batch_tag_success": "Batch tagging successful", "text_batch_operate_success": "Batch operation successful", "text_not_allowed_to_reply_ticket": "The selected tickets may include those closed by timeout, closed by rejection, or completed. Please check if the selected items are correct!", "text_batch_reply_error_message": "Batch Reply & Reject only support a single game. Please check if the selected items are correct!", "text_select_sure_batch_refuse": "Confirm all selected tickets for rejection", "text_start_QC": "Start Quality Check", "text_QC_info": "Quality Check Information", "text_QC_ispass": "Pass This Quality Check or Not", "text_QC_result_person": "Associate Quality Check Result with Employee(s)", "text_QC_result_to": "Sync Quality Check Result", "text_QC_problem_description": "Quality Check Issue Description", "text_modification_cause_remarks": "Modify Reason Note", "btn_submit_QC": "Submit Quality Check Result", "btn_edit_QC": "Modify Quality Check Result", "text_QC_score": "Quality Check Score (1–100)", "text_must_num": "Must be a number", "text_error_source": "Error Cause", "text_total_QCtask": "All Tasks", "text_pending_QCtask": "Pending Quality Check Tasks", "text_pending_QCtask_my": "My Pending Quality Checks", "text_finish_QCtask_my": "My Completed Quality Checks", "text_QC_failure_task": "Tasks with Failed Quality Checks", "text_QC_failure_task_my": "My Tasks with Failed Quality Checks", "text_submit_success": "Submission successful", "menu_sys_gameGoods_config": "Prop configuration  ", "text_goods_id": "Prop ID  ", "text_goods_name": "Prop name  ", "text_update_time": "Update time  ", "text_batch_upload": "Batch upload  ", "text_batch_add_gamegoods_temp": "Batch upload game prop template  ", "text_gold_query": "Gold query  ", "text_start_time": "Start Time", "text_over_time": "End time  ", "text_pay_query": "Payment query  ", "text_goods_query": "Item query  ", "text_login_query": "Login query  ", "text_chose_goods": "Please select an item  ", "text_question_type_manage": "Problem type  ", "text_add_question_type": "Add problem type  ", "text_edit_question_type": "Edit problem type  ", "text_questiuon_type_name": "Problem type name  ", "text_one_type": "Add first-level type  ", "text_last_replay_times": "Last reply time  ", "text_discord_question_config": "DC survey questionnaire configuration  ", "text_discord_satisfied_report": "DC satisfaction report  ", "text_btn_links": "Link  ", "text_question_link": "Survey questionnaire link  ", "text_one_star": "1 star  ", "text_two_star": "2 stars  ", "text_three_star": "3 stars  ", "text_four_star": "4 stars  ", "text_five_star": "5 stars  ", "text_scor_number": "Number of ratings  ", "text_four_and_five_star_account": "4/5 star ratio  ", "menu_sys_new_relationship": "Player relationship configuration  ", "menu_sys_new_tagconfig": "Tag configuration  ", "menu_sys_new_communication_records": "Communication record report  ", "text_player_line_name": "Player Line nickname  ", "text_player_line_id": "Player Line ID  ", "text_player_line_account": "Player Line account  ", "text_line_nick": "Line nickname  ", "text_line_pw": "Line account  ", "text_error_image": "Line official only supports jpeg and png image formats  ", "text_error_image_size": "Line official stipulates that the maximum image size does not exceed 10MB  ", "text_error_video": "Line official stipulates that videos only support mp4 format  ", "text_error_video_size": "Line official stipulates that the maximum video size does not exceed 200MB  ", "text_error_audio": "Line official stipulates that audio only supports mp3 format  ", "text_error_audio_size": "Line official stipulates that the maximum audio size does not exceed 5MB  ", "text_error_file": "Unsupported files  ", "text_line_bots": "Line channel  ", "text_user_type": "User type  ", "text_pay_type": "Payment type  ", "menu_sys_timeout_template_config": "Timeout reminder template configuration  ", "text_tip_time": "Pre-reminder time (time exceeding, unit: minutes)  ", "text_is_svip": "SVIP  ", "text_is_vipnew": "VIP-New  ", "text_new_template_type": "Template type management  ", "text_add_template_type": "Add new type  ", "text_edit_template_type": "Edit type  ", "text_template_type_name": "Template type name  ", "text_tpl_type": "Template classification  ", "text_batch_del_tag": "Batch delete tags  ", "text_public_label": "Public tags  ", "text_batch_remarks": "<PERSON><PERSON> remarks  ", "text_reopen_num": "Number of restarts  ", "text_upgrade_num": "Number of upgrades  ", "text_template_category": "Template type  ", "text_random_sampling": "Non-discriminatory Random Sampling", "text_equal_sampling": "Targeted Equal-Quantity Sampling", "text_channel_id": "Channel Number", "text_accept_type": "Processing Type", "menu_sys_ticket_corpus": "High-Quality Work Order Corpus", "menu_sys_training_results": "Training Results", "text_corpus_id": "Corpus ID", "text_corpus_content": "Corpus Content", "btn_train": "Training", "text_train_mode": "Model Training", "text_view_training_results": "View Training Results", "text_train_all_languages": "Train All", "text_train_selected_languages": "Train Specific Language", "text_please_select": "Please Select", "text_training_in_progress": "Training in Progress", "text_training_failed": "Training Failed", "text_training_success": "Training Successful", "text_batch_import": "Batch Import", "text_import_success": "Import Successful", "text_import_failed": "Import Failed", "text_please_upload_file_first": "Please Upload File First", "text_prepare_data_according_to_template": "Please Prepare Data According to the Template Format", "text_import_template_download": "Download Template Specification", "text_import_file_upload": "Please Select the File to Import", "text_import_file_upload_tip": "Only Excel Files Can Be Uploaded", "text_import_file_upload_btn": "Click to Upload", "text_operate_success": "Operate Successful", "text_training_time": "Training Time", "menu_sys_ai_strategy": "AI Strategy", "text_strategy_id": "Strategy ID", "text_strategy_name": "Strategy Name", "text_strategy_name_placeholder": "Please enter strategy name", "text_strategy_status": "Status", "text_strategy_enable": "Enable", "text_strategy_disable": "Disable", "text_strategy_edit": "Edit Strategy", "text_strategy_add": "Add Strategy", "text_strategy_delete": "Delete", "text_strategy_delete_confirm": "Are you sure to delete this strategy?", "text_filter_server_lists": "Servers", "text_filter_server_lists_placeholder": "Please enter servers", "text_filter_pay_range_lists": "Payment Range", "text_filter_pay_range_lists_placeholder": "Please enter payment range", "text_filter_castle_level_lists": "Castle Level", "text_filter_castle_level_lists_placeholder": "Please enter castle level", "text_operate_failed": "Operation failed", "text_strategy_name_max_length": "Strategy name maximum 100 characters", "text_pay_range_invalid": "Please enter an integer greater than or equal to 0", "text_castle_level_invalid": "Please enter an integer greater than or equal to 0", "text_ai_processing_conditions": "AI Processing Conditions", "text_ai_condition_description": "Note: Payment range and castle level, intersection of the two conditions", "text_strategy_exists_error": "Save failed! The game already has an associated strategy", "menu_dc_bot_config": "DC Bot Configuration", "text_input_dc_account": "Please input DC account", "text_input_server_name": "Please input server name", "text_input_application_id": "Please input Application ID", "text_input_welcome_msg": "Please input welcome message", "text_verify_server": "Verify Server", "text_publish": "Publish", "text_server_verify_success": "Server verification successful", "text_server_verify_failed": "Server verification failed", "text_please_verify_server_first": "Please verify server first", "text_publish_success": "Published successfully", "text_publish_failed_fill_all": "Please fill in all required fields", "text_processing": "Processing", "text_published": "Published", "text_publish_failed": "Publish failed", "text_input_public_key": "Please input Public Key", "text_input_token": "Please input Token", "text_input_server_id": "Please input Server ID", "text_dc_account": "DC Account", "text_server_name": "Server Name", "text_application_id": "Application ID", "text_public_key": "Public Key", "text_token": "Token", "text_welcome_msg": "Welcome Message", "text_operation_time": "Operation Time", "text_required_field": "This field is required", "text_welcome_message": "Welcome Message", "text_edit_welcome_message": "Edit Welcome Message", "text_save_success": "Save successfully", "text_save_failed": "Save failed", "text_welcome_message_placeholder": "Please input welcome message", "text_welcome_message_max_length": "Welcome message maximum 100 characters", "text_zone_vip_level": "Private Domain R Level", "text_invalid_projects": "No game permission for this ticket: ", "text_copy_success": "Copy successfully", "text_right_to_use": "Right to Use", "text_caution_handle": "Caut<PERSON>", "menu_mail": "Email", "menu_mail_statistical": "Report", "text_all_mail": "All Email", "text_pending_reply_message_email": "Pending Rep<PERSON>", "text_mine_pending_reply_email": "My Pending Reply Email", "text_batch_assign": "<PERSON><PERSON>", "text_email": "Email", "text_email_status": "Email Status", "text_keyword_search": "Keyword Search", "text_submitter_search": "Submitter Search", "text_email_id": "Email ID", "text_tags": "Tags", "text_processor": "Processor", "text_email_reply_time": "Email Reply Time", "text_email_create_time": "Email Create Time", "text_remark_search": "Remark Search", "text_pending_assign": "Pending Assign", "text_completed": "Completed", "text_rejected": "Rejected", "text_search_title_content": "Search title and content", "text_search_email_or_name": "Search email or sender name", "text_exact_match": "Exact match", "text_fuzzy_search": "Fuzzy search", "text_to": "to", "text_send_time_sort": "By Send Time", "text_wait_time_sort": "By Wait Time", "text_create_time_sort": "By Create Time", "text_active": "Active", "text_closed": "Closed", "text_archived": "Archived", "text_basic_info": "Basic Information", "text_email_content": "Email Content"}