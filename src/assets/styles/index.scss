@import './scroll-bar.scss';
* {
  outline: none;
}

body {
  height: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family:
    Helvetica Neue,
    Helvetica,
    PingFang SC,
    Hiragino Sans GB,
    Microsoft YaHei,
    Arial,
    sans-serif;
}

label {
  font-weight: 700;
}

html {
  height: 100%;
  box-sizing: border-box;
}

body,
html {
  width: 100%;
  overflow: hidden;
}

#app {
  width: 100%;
  height: 100%;
  position: relative;
  // background: radial-gradient(ellipse at bottom, #1b2735 0%, #454646 100%);
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

div:focus {
  outline: none;
}

.clearfix {
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: ' ';
    clear: both;
    height: 0;
  }
}

// global css
.main-header {
  background-color: #0a2140;
  box-shadow: 2px 2px 6px -1px rgba(0, 0, 0, 0.6);
  position: relative;
  height: 48px !important;
  z-index: 1002;
}
.main-container {
  height: 100%;
  width: 100%;

  .main-body {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: auto;
    // background-color: #f8f9fa;
  }
}
.page-view-wapper {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  .el-form {
    .el-form-item__label {
      pointer-events: none;
    }
  }
  .search-box {
    padding: 12px 17px 0px 17px;
    .el-form-item {
      margin-bottom: 12px !important;
      margin-right: 10px !important;
    }
  }
  .page-content-box {
    flex-grow: 1;
    background: #f8f9fa;
    box-sizing: border-box;
    padding: 10px 8px;
    overflow: hidden;
    .el-card {
      height: 100%;
      width: 100%;
      display: flex;
      flex-direction: column;
      .el-card__header,
      .el-card__footer {
        padding: 10px;
      }
      .el-card__footer {
        border: 0px;
      }
      .el-card__body {
        padding: 0px;
        flex-grow: 1;
        overflow: hidden;
      }
    }
  }
}
.el-dialog {
  .el-form {
    .el-form-item {
      margin-bottom: 24px;
    }
  }
}
.search-form {
  .el-form-item {
    margin-right: 5px !important;
    margin-left: 5px !important;
    margin-bottom: 8px;
  }
  .el-form-item__label {
    font-weight: 700;
  }
  .el-input {
    // width: auto !important;
    min-width: 200px;
  }
  .el-select {
    // width: auto !important;
    min-width: 200px;
  }
  .el-cascader {
    width: auto !important;
    min-width: 200px;
  }
  .el-date-editor {
    width: 410px !important;
  }
  .input-with-select .el-input-group__prepend {
    background-color: var(--el-fill-color-blank);
  }
}
.search-box {
  .el-form-item__label {
    font-weight: 700;
  }
}

@import '../icon/iconfont.css';
.main-view {
  .default-theme.splitpanes--vertical > .splitpanes__splitter {
    min-width: 6px;
    background: #f8f9fa;
  }

  .default-theme.splitpanes--horizontal > .splitpanes__splitter {
    min-height: 6px;
    background: #f8f9fa;
  }
  .splitpanes.default-theme .splitpanes__pane {
    background: #ffffff;
  }
}
.no-clearbtn {
  .el-button.el-button--small.is-text.el-picker-panel__link-btn {
    display: none !important;
  }
}

.el-popper {
  max-height: 600px;
  overflow: auto;
}
