export default {
  // 每行最大字符数
  printWidth: 100,
  // 缩进空格数
  tabWidth: 2,
  // 使用空格而不是制表符
  useTabs: false,
  // 语句末尾添加分号
  semi: true,
  // 使用单引号
  singleQuote: true,
  // 对象属性引号：仅在需要时添加
  quoteProps: 'as-needed',
  // JSX中使用单引号
  jsxSingleQuote: true,
  // 尾随逗号：ES5兼容
  trailingComma: 'es5',
  // 对象字面量的大括号间添加空格
  bracketSpacing: true,
  // JSX标签的>放在最后一行的末尾，而不是单独一行
  bracketSameLine: false,
  // 箭头函数参数括号：避免时省略
  arrowParens: 'avoid',
  // 换行符：自动
  endOfLine: 'auto',
  // Vue文件中的script和style标签缩进
  vueIndentScriptAndStyle: false,
  // HTML空白敏感性
  htmlWhitespaceSensitivity: 'css',
  // 嵌入式语言格式化
  embeddedLanguageFormatting: 'auto'
};
